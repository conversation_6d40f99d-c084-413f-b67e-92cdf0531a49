<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Smart Time Selector - Comprehensive Tests</title>
    <link rel="stylesheet" href="style.css" />
    <style>
      .test-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .test-section {
        margin-bottom: 40px;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
      }

      .test-section h2 {
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
      }

      .test-case {
        margin: 20px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
      }

      .test-case h3 {
        color: #495057;
        margin-top: 0;
      }

      .test-result {
        margin-top: 10px;
        padding: 10px;
        border-radius: 4px;
        font-weight: bold;
      }

      .test-result.pass {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .test-result.fail {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .test-result.pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }

      .event-log {
        background-color: #f1f3f4;
        border: 1px solid #dadce0;
        border-radius: 4px;
        padding: 10px;
        max-height: 200px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
      }

      .event-log-entry {
        margin: 2px 0;
        padding: 2px 4px;
        border-radius: 2px;
      }

      .event-log-entry.timeSlotChange {
        background-color: #e3f2fd;
      }

      .event-log-entry.smartBusinessHoursChange {
        background-color: #f3e5f5;
      }

      .controls {
        margin: 20px 0;
        padding: 15px;
        background-color: #e9ecef;
        border-radius: 4px;
      }

      .controls button {
        margin: 5px;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        background-color: #007bff;
        color: white;
        cursor: pointer;
      }

      .controls button:hover {
        background-color: #0056b3;
      }

      .controls button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>Smart Time Selector - Comprehensive Tests</h1>

      <div class="controls">
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearEventLog()">Clear Event Log</button>
        <button onclick="resetTestData()">Reset Test Data</button>
      </div>

      <div class="test-section">
        <h2>Event Monitoring</h2>
        <div class="event-log" id="eventLog">
          <div>Event log will appear here...</div>
        </div>
      </div>

      <div class="test-section">
        <h2>Cross-Slot Time Filtering Tests</h2>

        <div class="test-case">
          <h3>Test 1: New Slot Start Time Filtering</h3>
          <p>
            When adding a new time slot, the start time options should be
            filtered to only show times after the previous slot's end time.
          </p>
          <div class="test-result pending" id="test1Result">Pending</div>
          <button onclick="runTest1()">Run Test</button>
        </div>

        <div class="test-case">
          <h3>Test 2: Dynamic Slot Updates</h3>
          <p>
            When changing an existing slot's end time, subsequent slots should
            update their available start times.
          </p>
          <div class="test-result pending" id="test2Result">Pending</div>
          <button onclick="runTest2()">Run Test</button>
        </div>

        <div class="test-case">
          <h3>Test 3: Overlap Prevention</h3>
          <p>
            The system should prevent overlapping time ranges and show
            appropriate warnings.
          </p>
          <div class="test-result pending" id="test3Result">Pending</div>
          <button onclick="runTest3()">Run Test</button>
        </div>
      </div>

      <div class="test-section">
        <h2>Add Button State Management Tests</h2>

        <div class="test-case">
          <h3>Test 4: Add Button Disable When Full</h3>
          <p>
            Add button should be disabled when no more time slots can be added
            for the day.
          </p>
          <div class="test-result pending" id="test4Result">Pending</div>
          <button onclick="runTest4()">Run Test</button>
        </div>

        <div class="test-case">
          <h3>Test 5: Add Button Re-enable</h3>
          <p>
            Add button should be re-enabled when slots become available after
            removal.
          </p>
          <div class="test-result pending" id="test5Result">Pending</div>
          <button onclick="runTest5()">Run Test</button>
        </div>
      </div>

      <div class="test-section">
        <h2>UX Enhancement Tests</h2>

        <div class="test-case">
          <h3>Test 6: Validation Warnings</h3>
          <p>Invalid time selections should show inline warning messages.</p>
          <div class="test-result pending" id="test6Result">Pending</div>
          <button onclick="runTest6()">Run Test</button>
        </div>

        <div class="test-case">
          <h3>Test 7: Auto-Adjustment</h3>
          <p>Invalid times should be auto-adjusted when possible.</p>
          <div class="test-result pending" id="test7Result">Pending</div>
          <button onclick="runTest7()">Run Test</button>
        </div>
      </div>

      <div class="test-section">
        <h2>Custom Events Tests</h2>

        <div class="test-case">
          <h3>Test 8: timeSlotChange Event</h3>
          <p>
            timeSlotChange events should be dispatched with correct data
            structure.
          </p>
          <div class="test-result pending" id="test8Result">Pending</div>
          <button onclick="runTest8()">Run Test</button>
        </div>

        <div class="test-case">
          <h3>Test 9: smartBusinessHoursChange Event</h3>
          <p>
            smartBusinessHoursChange events should include comprehensive
            filtering metadata.
          </p>
          <div class="test-result pending" id="test9Result">Pending</div>
          <button onclick="runTest9()">Run Test</button>
        </div>
      </div>

      <!-- Business Hours Form for Testing -->
      <div class="test-section">
        <h2>Test Environment</h2>
        <div class="bd-container">
          <div id="business-hours"></div>
          <div id="scanner-hours"></div>
        </div>
      </div>
    </div>

    <script src="business-hours-manager.js"></script>
    <script>
      // Initialize the business hours manager for testing
      let businessHoursManager;
      let eventLog = [];

      document.addEventListener("DOMContentLoaded", function () {
        businessHoursManager = new BusinessHoursManager();
        businessHoursManager.init();

        // Set up event listeners for monitoring
        setupEventListeners();
      });

      function setupEventListeners() {
        document.addEventListener("timeSlotChange", function (event) {
          logEvent("timeSlotChange", event.detail);
        });

        document.addEventListener("smartBusinessHoursChange", function (event) {
          logEvent("smartBusinessHoursChange", event.detail);
        });

        document.addEventListener("businessHoursChange", function (event) {
          logEvent("businessHoursChange", event.detail);
        });
      }

      function logEvent(eventType, detail) {
        const timestamp = new Date().toLocaleTimeString();
        const entry = {
          timestamp,
          type: eventType,
          detail: detail,
        };

        eventLog.push(entry);
        updateEventLogDisplay();
      }

      function updateEventLogDisplay() {
        const logContainer = document.getElementById("eventLog");
        logContainer.innerHTML = eventLog
          .slice(-20)
          .map(
            (entry) =>
              `<div class="event-log-entry ${entry.type}">
                    [${entry.timestamp}] ${entry.type}: ${JSON.stringify(
                entry.detail,
                null,
                2
              ).substring(0, 100)}...
                </div>`
          )
          .join("");
        logContainer.scrollTop = logContainer.scrollHeight;
      }

      function clearEventLog() {
        eventLog = [];
        document.getElementById("eventLog").innerHTML =
          "<div>Event log cleared...</div>";
      }

      function resetTestData() {
        // Reset all test results
        for (let i = 1; i <= 9; i++) {
          const resultElement = document.getElementById(`test${i}Result`);
          if (resultElement) {
            resultElement.className = "test-result pending";
            resultElement.textContent = "Pending";
          }
        }

        // Reinitialize the business hours manager
        if (businessHoursManager) {
          businessHoursManager.init();
        }
      }

      // Test implementation functions
      async function runAllTests() {
        console.log("Running all tests...");
        for (let i = 1; i <= 9; i++) {
          await new Promise((resolve) => setTimeout(resolve, 500)); // Delay between tests
          window[`runTest${i}`]();
        }
      }

      function setTestResult(testId, passed, message) {
        const resultElement = document.getElementById(`test${testId}Result`);
        resultElement.className = `test-result ${passed ? "pass" : "fail"}`;
        resultElement.textContent = passed
          ? `PASS: ${message}`
          : `FAIL: ${message}`;
      }

      function runTest1() {
        console.log("Running Test 1: New Slot Start Time Filtering");
        try {
          // Enable Monday business hours
          const mondayToggle = document.getElementById("biz-monday-toggle");
          if (mondayToggle) {
            mondayToggle.checked = true;
            mondayToggle.dispatchEvent(new Event("change"));
          }

          // Set first slot to 09:00 - 12:00
          const startSelect = document.getElementById("biz-monday-start");
          const endSelect = document.getElementById("biz-monday-end");

          if (startSelect && endSelect) {
            startSelect.value = "09:00";
            startSelect.dispatchEvent(new Event("change"));
            endSelect.value = "12:00";
            endSelect.dispatchEvent(new Event("change"));

            // Add a new slot
            const addButton = document.querySelector(
              '[data-day="biz-monday"] .add-hours-btn'
            );
            if (addButton) {
              addButton.click();

              // Check if new slot's start time options are filtered
              setTimeout(() => {
                const newStartSelect =
                  document.getElementById("biz-monday-start-1");
                if (newStartSelect) {
                  const options = Array.from(newStartSelect.options);
                  const hasInvalidOptions = options.some(
                    (opt) => opt.value <= "12:00"
                  );

                  setTestResult(
                    1,
                    !hasInvalidOptions,
                    hasInvalidOptions
                      ? "New slot contains invalid start times"
                      : "Start times properly filtered"
                  );
                } else {
                  setTestResult(1, false, "New slot not created");
                }
              }, 100);
            } else {
              setTestResult(1, false, "Add button not found");
            }
          } else {
            setTestResult(1, false, "Time selects not found");
          }
        } catch (error) {
          setTestResult(1, false, `Error: ${error.message}`);
        }
      }

      function runTest2() {
        console.log("Running Test 2: Dynamic Slot Updates");
        try {
          // This test assumes Test 1 has run and created slots
          const firstEndSelect = document.getElementById("biz-monday-end");
          const secondStartSelect =
            document.getElementById("biz-monday-start-1");

          if (firstEndSelect && secondStartSelect) {
            const originalSecondStartValue = secondStartSelect.value;

            // Change first slot's end time
            firstEndSelect.value = "14:00";
            firstEndSelect.dispatchEvent(new Event("change"));

            setTimeout(() => {
              const newSecondStartOptions = Array.from(
                secondStartSelect.options
              );
              const hasValidOptions = newSecondStartOptions.some(
                (opt) => opt.value >= "14:00"
              );
              const hasInvalidOptions = newSecondStartOptions.some(
                (opt) => opt.value < "14:00"
              );

              setTestResult(
                2,
                hasValidOptions && !hasInvalidOptions,
                "Subsequent slot options updated correctly"
              );
            }, 100);
          } else {
            setTestResult(
              2,
              false,
              "Required elements not found - run Test 1 first"
            );
          }
        } catch (error) {
          setTestResult(2, false, `Error: ${error.message}`);
        }
      }

      function runTest3() {
        console.log("Running Test 3: Overlap Prevention");
        try {
          // Test validation system
          if (businessHoursManager) {
            const validation = businessHoursManager.validateTimeSlot(
              "biz",
              "monday",
              0
            );
            setTestResult(
              3,
              validation.isValid !== undefined,
              "Validation system is functional"
            );
          } else {
            setTestResult(3, false, "Business hours manager not initialized");
          }
        } catch (error) {
          setTestResult(3, false, `Error: ${error.message}`);
        }
      }

      function runTest4() {
        console.log("Running Test 4: Add Button Disable When Full");
        try {
          if (businessHoursManager) {
            const canAdd = businessHoursManager.canAddMoreSlots(
              "biz",
              "monday"
            );
            const addButton = document.querySelector(
              '[data-day="biz-monday"] .add-hours-btn'
            );

            if (addButton) {
              const isDisabled = addButton.disabled;
              const shouldBeDisabled = !canAdd;

              setTestResult(
                4,
                isDisabled === shouldBeDisabled,
                `Add button state matches availability (disabled: ${isDisabled}, should be: ${shouldBeDisabled})`
              );
            } else {
              setTestResult(4, false, "Add button not found");
            }
          } else {
            setTestResult(4, false, "Business hours manager not initialized");
          }
        } catch (error) {
          setTestResult(4, false, `Error: ${error.message}`);
        }
      }

      function runTest5() {
        console.log("Running Test 5: Add Button Re-enable");
        try {
          // Remove a slot if it exists
          const removeButton = document.querySelector(
            ".additional-time-slot .remove-hours-btn"
          );
          if (removeButton) {
            removeButton.click();

            setTimeout(() => {
              const addButton = document.querySelector(
                '[data-day="biz-monday"] .add-hours-btn'
              );
              if (addButton && !addButton.disabled) {
                setTestResult(
                  5,
                  true,
                  "Add button re-enabled after slot removal"
                );
              } else {
                setTestResult(5, false, "Add button not re-enabled");
              }
            }, 400); // Wait for removal animation
          } else {
            setTestResult(
              5,
              false,
              "No additional slots to remove - run previous tests first"
            );
          }
        } catch (error) {
          setTestResult(5, false, `Error: ${error.message}`);
        }
      }

      function runTest6() {
        console.log("Running Test 6: Validation Warnings");
        try {
          if (businessHoursManager) {
            // Test showing a warning
            businessHoursManager.showTimeSlotWarning(
              "biz",
              "monday",
              0,
              "Test warning message"
            );

            setTimeout(() => {
              const warningElement = document.querySelector(
                '.time-slot-warning[data-slot-index="0"]'
              );
              if (warningElement) {
                setTestResult(6, true, "Warning message displayed correctly");
                // Clear the warning
                businessHoursManager.clearTimeSlotWarning("biz", "monday", 0);
              } else {
                setTestResult(6, false, "Warning message not displayed");
              }
            }, 100);
          } else {
            setTestResult(6, false, "Business hours manager not initialized");
          }
        } catch (error) {
          setTestResult(6, false, `Error: ${error.message}`);
        }
      }

      function runTest7() {
        console.log("Running Test 7: Auto-Adjustment");
        try {
          if (businessHoursManager) {
            // Test auto-adjustment functionality
            const adjusted = businessHoursManager.autoAdjustTimeSlot(
              "biz",
              "monday",
              0
            );
            setTestResult(
              7,
              adjusted !== undefined,
              "Auto-adjustment function is available"
            );
          } else {
            setTestResult(7, false, "Business hours manager not initialized");
          }
        } catch (error) {
          setTestResult(7, false, `Error: ${error.message}`);
        }
      }

      function runTest8() {
        console.log("Running Test 8: timeSlotChange Event");
        try {
          let eventReceived = false;
          const eventHandler = (event) => {
            if (
              event.detail.prefix &&
              event.detail.day &&
              event.detail.slots !== undefined
            ) {
              eventReceived = true;
              setTestResult(
                8,
                true,
                "timeSlotChange event structure is correct"
              );
            } else {
              setTestResult(
                8,
                false,
                "timeSlotChange event structure is invalid"
              );
            }
            document.removeEventListener("timeSlotChange", eventHandler);
          };

          document.addEventListener("timeSlotChange", eventHandler);

          // Trigger an event by changing a time
          const startSelect = document.getElementById("biz-monday-start");
          if (startSelect) {
            startSelect.value = "10:00";
            startSelect.dispatchEvent(new Event("change"));

            setTimeout(() => {
              if (!eventReceived) {
                setTestResult(8, false, "timeSlotChange event not received");
                document.removeEventListener("timeSlotChange", eventHandler);
              }
            }, 200);
          } else {
            setTestResult(8, false, "Start select not found");
          }
        } catch (error) {
          setTestResult(8, false, `Error: ${error.message}`);
        }
      }

      function runTest9() {
        console.log("Running Test 9: smartBusinessHoursChange Event");
        try {
          let eventReceived = false;
          const eventHandler = (event) => {
            if (
              event.detail.allData &&
              event.detail.smartFiltering &&
              event.detail.version === "2.0"
            ) {
              eventReceived = true;
              setTestResult(
                9,
                true,
                "smartBusinessHoursChange event structure is correct"
              );
            } else {
              setTestResult(
                9,
                false,
                "smartBusinessHoursChange event structure is invalid"
              );
            }
            document.removeEventListener(
              "smartBusinessHoursChange",
              eventHandler
            );
          };

          document.addEventListener("smartBusinessHoursChange", eventHandler);

          // Trigger the smart event
          if (businessHoursManager) {
            businessHoursManager.dispatchSmartBusinessHoursChangeEvent();

            setTimeout(() => {
              if (!eventReceived) {
                setTestResult(
                  9,
                  false,
                  "smartBusinessHoursChange event not received"
                );
                document.removeEventListener(
                  "smartBusinessHoursChange",
                  eventHandler
                );
              }
            }, 200);
          } else {
            setTestResult(9, false, "Business hours manager not initialized");
          }
        } catch (error) {
          setTestResult(9, false, `Error: ${error.message}`);
        }
      }
    </script>
  </body>
</html>
