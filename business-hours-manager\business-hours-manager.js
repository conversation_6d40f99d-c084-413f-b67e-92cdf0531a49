class BusinessHoursManager {
    constructor(element = document) {
        this.element = element;
        this.initialized = false;

        // Initialize constants and configurations
        this.initializeConstants();
        this.timeConfigInitialization();
    }

    /**
     * Initialize constants and configuration
     */
    initializeConstants() {
        this.DAYS = [
            { key: "sun", name: "Sunday", isWeekday: false },
            { key: "mon", name: "Monday", isWeekday: true },
            { key: "tue", name: "Tuesday", isWeekday: true },
            { key: "wed", name: "Wednesday", isWeekday: true },
            { key: "thu", name: "Thursday", isWeekday: true },
            { key: "fri", name: "Friday", isWeekday: true },
            { key: "sat", name: "Saturday", isWeekday: false },
        ];
        this.DAYS_CONFIG = {
            KEYS: this.DAYS.map(day => day.key.toLowerCase()),
            NAMES: this.DAYS.map(day => day.name),
        };

        this.elements = {
            scannerHoursSection: document.getElementById("scanner-hours"),
            scannerHoursContainer: document.querySelector(".scanner-hours-container"),
            sameHoursCheck: document.getElementById("same-hours"),
        }
    }

    /**
     * Initialize time configuration with AM/PM formatting
     */
    timeConfigInitialization() {
        const timeSlots = ["06:00", "06:30", "07:00", "07:30", "08:00", "08:30", "09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "12:00", "12:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00", "17:30", "18:00", "18:30", "19:00", "19:30", "20:00", "20:30", "21:00", "21:30", "22:00", "22:30", "23:00",];

        this.timeConfig = {
            options: timeSlots.map((timeValue) => {
                const [hourStr, minuteStr] = timeValue.split(":");
                const hour = parseInt(hourStr, 10);
                const displayHour = hour === 12 ? 12 : hour > 12 ? hour - 12 : hour;
                const period = hour >= 12 ? "PM" : "AM";
                const label = `${displayHour
                    .toString()
                    .padStart(2, "0")}:${minuteStr} ${period}`;

                return { value: timeValue, label: label };
            }),
        };
    }

    /**
     * Generate time options HTML from configuration
     * @param {Array} options - Array of time option objects with 'value' and 'label'
     * @param {String} selectedValue - Value to be selected by default
     */
    generateTimeOptionsHTML(options, selectedValue = null) {
        return options
            .map((option) => {
                const isSelected = selectedValue
                    ? option.value === selectedValue
                    : option.selected || false;
                return `<option value="${option.value}" ${isSelected ? "selected" : ""
                    }>${option.label}</option>`;
            })
            .join("");
    }

    /**
     * Update select element options safely to prevent duplicates
     * @param {HTMLSelectElement} selectElement - The select element to update
     * @param {Array} options - Array of time option objects
     * @param {String} selectedValue - Value to be selected by default
     */
    updateSelectOptions(selectElement, options, selectedValue = null) {
        selectElement.innerHTML = "";

        options.forEach((option) => {
            const optionElement = document.createElement("option");

            optionElement.value = option.value;
            optionElement.textContent = option.label;

            if (selectedValue && option.value === selectedValue) {
                optionElement.selected = true;
            }

            selectElement.appendChild(optionElement);
        });
    }

    /**
     * Initialize Facility Info Step
     * Sets up time options and "same as business hours" functionality
     */
    initializeFacilityInfoStep() {
        this.generateHoursSection("business");
        this.generateHoursSection("scanner");

        // Set up event listeners after generating sections
        this.setupHoursEventListeners();

        // Same as Business Hours toggle
        const { sameHoursCheck, scannerHoursSection, scannerHoursContainer } = this.elements;
        if (sameHoursCheck) {
            if (!sameHoursCheck.checked) {
                sameHoursCheck.checked = true;
                this.hideScannerHours(scannerHoursSection, scannerHoursContainer);
            } else if (sameHoursCheck.checked) {
                this.hideScannerHours(scannerHoursSection, scannerHoursContainer);
            }

            sameHoursCheck.addEventListener("change", () => {
                if (sameHoursCheck.checked) {
                    this.hideScannerHours(scannerHoursSection, scannerHoursContainer);
                } else {
                    this.showScannerHours(scannerHoursSection, scannerHoursContainer);

                    setTimeout(() => {
                        this.generateHoursSection("scanner");
                        this.setupHoursEventListeners();
                        this.saveCurrentData();
                    }, 200);
                }
            });
        }
    }

    /**
     * Generate dynamic hours section
     * @param {string} type - Either 'business' or 'scanner'
     */
    generateHoursSection(type) {
        const prefix = type === "business" ? "biz" : "scan";
        const containerId =
            type === "business" ? "business-hours" : "scanner-hours";

        const container = document.getElementById(containerId);
        if (!container) return;

        // Clear existing content
        container.innerHTML = "";

        // Generate each day row
        this.DAYS.forEach((dayConfig) => {
            const dayRow = this.createDayRow(prefix, dayConfig.key, dayConfig.name);
            container.appendChild(dayRow);
        });
    }

    /**
     * Create a single day row
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day key (e.g., 'mon', 'tue')
     * @param {string} label - Display label for the day
     */
    createDayRow(prefix, day, label) {
        const row = document.createElement("div");
        row.className = "bd-hours-row";

        const dayConfig = this.DAYS.find(d => d.key === day);
        const isChecked = dayConfig ? dayConfig.isWeekday : false;

        row.innerHTML = `<div class="toggle-switch-container"><div class="toggle-switch"><input type="checkbox" id="${prefix}-${day}" name="${prefix}-${day}" class="day-toggle" data-day="${day}" ${isChecked ? "checked" : ""}/><label for="${prefix}-${day}" class="toggle-slider"></label></div><label for="${prefix}-${day}">${label.slice(0, 3)}</label></div><div class="time-inputs-container"><div class="time-inputs-default">${isChecked ? this.createTimeInputsHTML(prefix, day) : ""}${!isChecked ? '<span class="unavailable-text">Unavailable</span>' : ""}${isChecked ? `<button class="add-hours-btn plus-icon" type="button"></button>` : ""}</div></div>`;
        return row;
    }

    /**
     * Create time inputs HTML
     */
    createTimeInputsHTML(prefix, day) {
        const timeOptions = this.getTimeOptions();

        return `<div class="time-inputs"><select id="${prefix}-${day}-start" name="${prefix}-${day}-start" class="time-select">${timeOptions}</select><span>-</span><select id="${prefix}-${day}-end" name="${prefix}-${day}-end" class="time-select">${timeOptions}</select></div>`;
    }

    /**
     * Get time options HTML
     */
    getTimeOptions() {
        return this.generateTimeOptionsHTML(this.timeConfig.options);
    }

    /**
     * Setup event listeners for hours sections
     */
    setupHoursEventListeners() {
        const dayToggles = document.querySelectorAll(".day-toggle");
        dayToggles.forEach((toggle) => {
            // Check if event listener already exists
            if (!toggle.hasAttribute("data-listener-added")) {
                toggle.addEventListener("change", () => {
                    this.toggleDayAvailability(toggle);
                    this.saveCurrentData();
                });
                toggle.setAttribute("data-listener-added", "true");
            }

            // Initialize toggle state on load
            this.toggleDayAvailability(toggle);
        });

        // Add time slot buttons - only add listeners to buttons that don't have them
        const addHoursBtns = document.querySelectorAll(
            ".add-hours-btn:not([data-listener-added])"
        );
        addHoursBtns.forEach((btn) => {
            btn.addEventListener("click", this.addTimeSlot.bind(this));
            btn.setAttribute("data-listener-added", "true");
        });

        // Add smart filtering and auto-save functionality to all existing time select dropdowns
        const timeSelects = document.querySelectorAll(
            ".time-select:not([data-listener-added])"
        );
        timeSelects.forEach((select) => {
            select.addEventListener("change", () => {
                // If start time changed, update corresponding end time options with smart filtering
                const startMatch = select.id.match(/^(.+)-start(-\d*)?$/);
                if (startMatch) {
                    const base = startMatch[1];
                    const suffix = startMatch[2] || "";
                    const endSelect = document.getElementById(`${base}-end${suffix}`);
                    if (endSelect) {
                        // Get current end value to preserve if still valid
                        const currentEndValue = endSelect.value;

                        // Filter options to be greater than selected start value
                        const filteredOptions = this.timeConfig.options.filter(
                            (opt) => opt.value > select.value
                        );

                        // Determine which value to select
                        let valueToSelect = null;
                        if (
                            currentEndValue &&
                            filteredOptions.some((opt) => opt.value === currentEndValue)
                        ) {
                            valueToSelect = currentEndValue;
                        } else if (filteredOptions.length > 0) {
                            valueToSelect = filteredOptions[0].value;
                        }

                        // Clear existing options and generate new ones with proper selection
                        this.updateSelectOptions(endSelect, filteredOptions, valueToSelect);
                    }
                }
                this.saveCurrentData();
            });
            select.setAttribute("data-listener-added", "true");
        });

        // Initialize end time options based on current start values
        timeSelects.forEach((select) => {
            if (select.id.match(/^(.+)-start(-\d*)?$/)) {
                // Only initialize if not already initialized
                if (!select.hasAttribute("data-initialized")) {
                    select.dispatchEvent(new Event("change"));
                    select.setAttribute("data-initialized", "true");
                }
            }
        });
    }

    /**
     * Toggle day availability
     * @param {HTMLInputElement} toggle - The day toggle checkbox
     */
    toggleDayAvailability(toggle) {
        const row = toggle.closest(".bd-hours-row");
        const timeInputsContainer = row.querySelector(".time-inputs-container");
        const timeInputsDefault = timeInputsContainer.querySelector(
            ".time-inputs-default"
        );
        const timeInputs = timeInputsDefault.querySelector(".time-inputs");
        const addBtn = timeInputsDefault.querySelector(".add-hours-btn.plus-icon");
        const unavailableText =
            timeInputsDefault.querySelector(".unavailable-text");
        const additionalTimeSlots = timeInputsContainer.querySelectorAll(
            ".additional-time-slot"
        );

        if (toggle.checked) {
            if (!timeInputs) {
                this.createTimeInputs(row, toggle);
            } else {
                timeInputs.style.display = "flex";
                if (addBtn) addBtn.style.display = "flex";
            }

            if (unavailableText) {
                unavailableText.style.display = "none";
            }
        } else {
            if (timeInputs) {
                timeInputs.style.display = "none";
            }
            if (addBtn) {
                addBtn.style.display = "none";
            }

            additionalTimeSlots.forEach((slot) => {
                slot.remove();
            });

            if (unavailableText) {
                unavailableText.style.display = "inline";
            } else {
                const newUnavailableText = document.createElement("span");
                newUnavailableText.className = "unavailable-text";
                newUnavailableText.textContent = "Unavailable";
                timeInputsDefault.appendChild(newUnavailableText);
            }
        }

        // Dispatch specific event based on the type (business or scanner)
        const prefix = toggle.id.split("-")[0]; // 'biz' or 'scan'

        if (prefix === "biz") {
            this.dispatchBusinessHoursChangeEvent();
        } else if (prefix === "scan") {
            this.dispatchScannerHoursChangeEvent();
        }
    }

    /**
     * Create time inputs for a day
     * @param {HTMLElement} row - The day row element
     * @param {HTMLInputElement} toggle - The day toggle checkbox
     */
    createTimeInputs(row, toggle) {
        const prefix = toggle.id.split("-")[0]; // 'biz' or 'scan'
        const day = toggle.id.split("-")[1]; // day name
        const timeInputsContainer = row.querySelector(".time-inputs-container");
        const timeInputsDefault = timeInputsContainer.querySelector(
            ".time-inputs-default"
        );

        // Remove unavailable text if it exists
        const unavailableText =
            timeInputsDefault.querySelector(".unavailable-text");
        if (unavailableText) {
            unavailableText.remove();
        }

        const newTimeInputs = document.createElement("div");
        newTimeInputs.className = "time-inputs";

        // Generate options using global configuration
        const timeOptions = this.getTimeOptions();

        newTimeInputs.innerHTML = `
            <select id="${prefix}-${day}-start" name="${prefix}-${day}-start" class="time-select">
                ${timeOptions}
            </select>
            <span>-</span>
            <select id="${prefix}-${day}-end" name="${prefix}-${day}-end" class="time-select">
                ${timeOptions}
            </select>
        `;

        // Add to time-inputs-default container
        timeInputsDefault.appendChild(newTimeInputs);

        // Create add button
        const newAddBtn = document.createElement("button");
        newAddBtn.className = "add-hours-btn plus-icon";
        newAddBtn.type = "button";

        newAddBtn.addEventListener("click", this.addTimeSlot.bind(this));
        newAddBtn.setAttribute("data-listener-added", "true");
        timeInputsDefault.appendChild(newAddBtn);
    }

    /**
     * Add time slot functionality
     * @param {Event} e - The click event
     */
    addTimeSlot(e) {
        const row = e.target.closest(".bd-hours-row");
        const timeInputsContainer = row.querySelector(".time-inputs-container");

        // Get prefix and day from the existing toggle
        const toggle = row.querySelector(".day-toggle");
        const prefix = toggle.id.split("-")[0]; // 'biz' or 'scan'
        const day = toggle.id.split("-")[1]; // day name

        // Generate unique index for additional time slots
        const existingSlots = timeInputsContainer.querySelectorAll(
            ".additional-time-slot"
        );
        const slotIndex = existingSlots.length + 1;

        // Create a new time slot div
        const newTimeSlot = document.createElement("div");
        newTimeSlot.className = "additional-time-slot";

        // Create time inputs with proper IDs and names using global configuration
        const timeInputs = document.createElement("div");
        timeInputs.className = "time-inputs";

        // Generate smart time options - start with default values but filter end options
        const startOptions = this.generateTimeOptionsHTML(
            this.timeConfig.options,
            "12:00"
        );
        const filteredEndOptions = this.timeConfig.options.filter(
            (opt) => opt.value > "12:00"
        );
        const endOptions = this.generateTimeOptionsHTML(
            filteredEndOptions,
            "18:00"
        );

        timeInputs.innerHTML = `
            <select id="${prefix}-${day}-start-${slotIndex}" name="${prefix}-${day}-start-${slotIndex}" class="time-select">
                ${startOptions}
            </select>
            <span>-</span>
            <select id="${prefix}-${day}-end-${slotIndex}" name="${prefix}-${day}-end-${slotIndex}" class="time-select">
                ${endOptions}
            </select>
        `;

        // Create remove button
        const removeBtn = document.createElement("button");
        removeBtn.className = "add-hours-btn remove-hours-btn";
        removeBtn.type = "button";
        removeBtn.style.backgroundColor = "#EBEBEB";
        removeBtn.setAttribute("aria-label", "Remove time slot");

        removeBtn.addEventListener("click", () => {
            this.removeTimeSlot(newTimeSlot);
            // Auto-save data after removing time slot
            setTimeout(() => {
                this.saveCurrentData();
            }, 350); // Slight delay to ensure removal animation completes
        });

        // Add time inputs and remove button to the new time slot
        newTimeSlot.appendChild(timeInputs);
        newTimeSlot.appendChild(removeBtn);

        // Set initial state for animation
        newTimeSlot.style.opacity = "0";
        newTimeSlot.style.transform = "translateY(-10px) scale(0.95)";
        newTimeSlot.style.maxHeight = "0px";
        newTimeSlot.style.overflow = "hidden";
        newTimeSlot.style.transition = "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)";

        // Add the additional time slot at the END (append to container)
        timeInputsContainer.appendChild(newTimeSlot);

        // Trigger animation on next frame
        requestAnimationFrame(() => {
            newTimeSlot.style.opacity = "1";
            newTimeSlot.style.transform = "translateY(0) scale(1)";
            newTimeSlot.style.maxHeight = "60px"; // Adjust based on actual content height

            // Remove maxHeight after animation completes
            setTimeout(() => {
                newTimeSlot.style.maxHeight = "";
                newTimeSlot.style.overflow = "";

                // Add smart filtering event listeners to the new time select dropdowns
                const timeSelects = newTimeSlot.querySelectorAll(".time-select");
                timeSelects.forEach((select) => {
                    select.addEventListener("change", () => {
                        // If start time changed, update corresponding end time options
                        const startMatch = select.id.match(/^(.+)-start(-\d*)?$/);
                        if (startMatch) {
                            const base = startMatch[1];
                            const suffix = startMatch[2] || "";
                            const endSelect = document.getElementById(`${base}-end${suffix}`);
                            if (endSelect) {
                                // Filter options to be greater than selected start value
                                const filteredOptions = this.timeConfig.options.filter(
                                    (opt) => opt.value > select.value
                                );
                                this.updateSelectOptions(endSelect, filteredOptions);
                            }
                        }
                        this.saveCurrentData();
                    });
                    select.setAttribute("data-listener-added", "true");
                });

                // Initialize end time options based on current start value
                const startSelect = newTimeSlot.querySelector(
                    `[id$="-start-${slotIndex}"]`
                );
                if (startSelect) {
                    startSelect.dispatchEvent(new Event("change"));
                }

                // Auto-save data after adding time slot
                this.saveCurrentData();
            }, 300);
        });
    }

    /**
     * Remove time slot functionality with smooth animation
     * @param {HTMLElement} timeSlotElement - The time slot element to remove
     */
    removeTimeSlot(timeSlotElement) {
        if (timeSlotElement && timeSlotElement.parentNode) {
            // Set up animation styles
            timeSlotElement.style.transition =
                "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)";
            timeSlotElement.style.transformOrigin = "center";

            // Get current height for smooth collapse animation
            const currentHeight = timeSlotElement.offsetHeight;
            timeSlotElement.style.height = currentHeight + "px";

            // Force a reflow to ensure the height is set
            timeSlotElement.offsetHeight;

            // Start the removal animation
            timeSlotElement.style.opacity = "0";
            timeSlotElement.style.transform = "translateY(-10px) scale(0.95)";
            timeSlotElement.style.height = "0px";
            timeSlotElement.style.marginBottom = "0px";
            timeSlotElement.style.paddingTop = "0px";
            timeSlotElement.style.paddingBottom = "0px";

            // Remove element after animation completes
            setTimeout(() => {
                if (timeSlotElement && timeSlotElement.parentNode) {
                    timeSlotElement.parentNode.removeChild(timeSlotElement);
                }
            }, 300);
        }
    }

    /**
     * Hide scanner hours with smooth animation
     * @param {HTMLElement} scannerHoursSection - Scanner hours section element
     * @param {HTMLElement} scannerHoursContainer - Scanner hours container element
     */
    hideScannerHours(scannerHoursSection, scannerHoursContainer) {
        if (scannerHoursSection) {
            scannerHoursSection.classList.add("hiding");
            scannerHoursSection.classList.remove("showing");
        }

        if (scannerHoursContainer) {
            scannerHoursContainer.classList.add("sync-enabled");
            scannerHoursContainer.querySelector("label").classList.add("checked");
        }
    }

    /**
     * Show scanner hours with smooth animation
     * @param {HTMLElement} scannerHoursSection - Scanner hours section element
     * @param {HTMLElement} scannerHoursContainer - Scanner hours container element
     */
    showScannerHours(scannerHoursSection, scannerHoursContainer) {
        if (scannerHoursContainer) {
            scannerHoursContainer.classList.remove("sync-enabled");
            scannerHoursContainer.querySelector("label").classList.remove("checked");
        }

        if (scannerHoursSection) {
            scannerHoursSection.classList.remove("hiding");
            scannerHoursSection.classList.add("showing");
        }
    }

    /**
     * Save current data and dispatch change event
     * This method can be overridden or provided by the main form class
     */
    saveCurrentData() {
        this.dispatchChangeEvent();
    }

    /**
     * Dispatch custom change event with business hours data
     * Similar to custom-select.js event system
     */
    dispatchChangeEvent() {
        const businessHoursData = this.getHoursData("biz");
        const scannerHoursData = this.getHoursData("scan");

        const event = new CustomEvent("businessHoursChange", {
            detail: {
                businessHours: businessHoursData,
                scannerHours: scannerHoursData,
                allData: this.getAllHoursData(),
                timestamp: new Date().toISOString(),
            },
            bubbles: true,
            cancelable: true,
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Dispatch specific event for business hours only
     */
    dispatchBusinessHoursChangeEvent() {
        const businessHoursData = this.getHoursData("biz");

        const event = new CustomEvent("businessHoursOnlyChange", {
            detail: {
                businessHours: businessHoursData,
                type: "business",
                timestamp: new Date().toISOString(),
            },
            bubbles: true,
            cancelable: true,
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Dispatch specific event for scanner hours only
     */
    dispatchScannerHoursChangeEvent() {
        const scannerHoursData = this.getHoursData("scan");

        const event = new CustomEvent("scannerHoursOnlyChange", {
            detail: {
                scannerHours: scannerHoursData,
                type: "scanner",
                timestamp: new Date().toISOString(),
            },
            bubbles: true,
            cancelable: true,
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Get all hours data in a comprehensive format
     * @returns {Object} - Complete hours data structure
     */
    getAllHoursData() {
        return {
            business: this.getHoursData("biz"),
            scanner: this.getHoursData("scan"),
            sameHours: this.isSameHoursEnabled(),
        };
    }

    /**
     * Check if "same hours" option is enabled
     * @returns {boolean} - True if same hours is checked
     */
    isSameHoursEnabled() {
        const sameHoursCheck = this.element.sameHoursCheck;
        return sameHoursCheck ? sameHoursCheck.checked : false;
    }

    /**
     * Get business hours data in structured format
     * @param {string} prefix - Either 'biz' or 'scan'
     * @returns {Array} - Array of hours data for each day
     */
    getHoursData(prefix) {
        const days = this.DAYS_CONFIG.KEYS;
        const dayNames = this.DAYS_CONFIG.NAMES;
        const hoursData = [];

        days.forEach((day, index) => {
            const toggle = document.getElementById(`${prefix}-${day}`);

            // Determine if day is closed (unavailable)
            const isClosed = !toggle || !toggle.checked;

            const dayData = {
                day: dayNames[index],
                is_closed: isClosed,
                is_open_24: false,
                hours: [],
            };

            // Only process hours if day is enabled (not closed)
            if (!isClosed) {
                const startTime = document.getElementById(`${prefix}-${day}-start`);
                const endTime = document.getElementById(`${prefix}-${day}-end`);

                if (startTime && endTime && startTime.value && endTime.value) {
                    dayData.hours.push({
                        open_time: startTime.value,
                        close_time: endTime.value,
                    });
                }

                // Get additional time slots
                let slotIndex = 1;
                let startTimeSlot = document.getElementById(
                    `${prefix}-${day}-start-${slotIndex}`
                );
                let endTimeSlot = document.getElementById(
                    `${prefix}-${day}-end-${slotIndex}`
                );

                while (
                    startTimeSlot &&
                    endTimeSlot &&
                    startTimeSlot.value &&
                    endTimeSlot.value
                ) {
                    dayData.hours.push({
                        open_time: startTimeSlot.value,
                        close_time: endTimeSlot.value,
                    });
                    slotIndex++;
                    startTimeSlot = document.getElementById(
                        `${prefix}-${day}-start-${slotIndex}`
                    );
                    endTimeSlot = document.getElementById(
                        `${prefix}-${day}-end-${slotIndex}`
                    );
                }
            }

            // Always add all days (both open and closed)
            hoursData.push(dayData);
        });

        return hoursData;
    }

    /**
     * Initialize the business hours manager
     * This should be called after the DOM is ready
     */
    init() {
        if (this.initialized) {
            return;
        }

        this.initializeFacilityInfoStep();
        this.initialized = true;
    }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
    if (typeof window !== "undefined") {
        window.BusinessHoursManager = new BusinessHoursManager();
        window.BusinessHoursManager.init();
    }
});
