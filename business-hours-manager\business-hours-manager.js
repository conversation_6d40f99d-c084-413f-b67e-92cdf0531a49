class BusinessHoursManager {
    constructor(element = document) {
        this.element = element;
        this.initialized = false;

        // Initialize constants and configurations
        this.initializeConstants();
        this.timeConfigInitialization();
    }

    /**
     * Initialize constants and configuration
     */
    initializeConstants() {
        this.DAYS = [
            { key: "sun", name: "Sunday", isWeekday: false },
            { key: "mon", name: "Monday", isWeekday: true },
            { key: "tue", name: "Tuesday", isWeekday: true },
            { key: "wed", name: "Wednesday", isWeekday: true },
            { key: "thu", name: "Thursday", isWeekday: true },
            { key: "fri", name: "Friday", isWeekday: true },
            { key: "sat", name: "Saturday", isWeekday: false },
        ];
        this.DAYS_CONFIG = {
            KEYS: this.DAYS.map(day => day.key.toLowerCase()),
            NAMES: this.DAYS.map(day => day.name),
        };

        this.elements = {
            scannerHoursSection: document.getElementById("scanner-hours"),
            scannerHoursContainer: document.querySelector(".scanner-hours-container"),
            sameHoursCheck: document.getElementById("same-hours"),
        }
    }

    /**
     * Initialize time configuration with AM/PM formatting
     */
    timeConfigInitialization() {
        const timeSlots = ["06:00", "06:30", "07:00", "07:30", "08:00", "08:30", "09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "12:00", "12:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00", "17:30", "18:00", "18:30", "19:00", "19:30", "20:00", "20:30", "21:00", "21:30", "22:00", "22:30", "23:00",];

        this.timeConfig = {
            options: timeSlots.map((timeValue) => {
                const [hourStr, minuteStr] = timeValue.split(":");
                const hour = parseInt(hourStr, 10);
                const displayHour = hour === 12 ? 12 : hour > 12 ? hour - 12 : hour;
                const period = hour >= 12 ? "PM" : "AM";
                const label = `${displayHour
                    .toString()
                    .padStart(2, "0")}:${minuteStr} ${period}`;

                return { value: timeValue, label: label };
            }),
        };
    }

    /**
     * Generate time options HTML from configuration
     * @param {Array} options - Array of time option objects with 'value' and 'label'
     * @param {String} selectedValue - Value to be selected by default
     */
    generateTimeOptionsHTML(options, selectedValue = null) {
        return options
            .map((option) => {
                const isSelected = selectedValue
                    ? option.value === selectedValue
                    : option.selected || false;
                return `<option value="${option.value}" ${isSelected ? "selected" : ""
                    }>${option.label}</option>`;
            })
            .join("");
    }

    /**
     * Update select element options safely to prevent duplicates
     * @param {HTMLSelectElement} selectElement - The select element to update
     * @param {Array} options - Array of time option objects
     * @param {String} selectedValue - Value to be selected by default
     */
    updateSelectOptions(selectElement, options, selectedValue = null) {
        selectElement.innerHTML = "";

        options.forEach((option) => {
            const optionElement = document.createElement("option");

            optionElement.value = option.value;
            optionElement.textContent = option.label;

            if (selectedValue && option.value === selectedValue) {
                optionElement.selected = true;
            }

            selectElement.appendChild(optionElement);
        });
    }

    /**
     * Get all time slots for a specific day in chronological order
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name (e.g., 'monday', 'tuesday')
     * @returns {Array} Array of time slot objects with start and end times
     */
    getDayTimeSlots(prefix, day) {
        const slots = [];

        // Get the main time slot
        const mainStart = document.getElementById(`${prefix}-${day}-start`);
        const mainEnd = document.getElementById(`${prefix}-${day}-end`);

        if (mainStart && mainEnd && mainStart.value && mainEnd.value) {
            slots.push({
                startElement: mainStart,
                endElement: mainEnd,
                startTime: mainStart.value,
                endTime: mainEnd.value,
                index: 0
            });
        }

        // Get additional time slots
        let slotIndex = 1;
        let startElement = document.getElementById(`${prefix}-${day}-start-${slotIndex}`);
        let endElement = document.getElementById(`${prefix}-${day}-end-${slotIndex}`);

        while (startElement && endElement) {
            if (startElement.value && endElement.value) {
                slots.push({
                    startElement: startElement,
                    endElement: endElement,
                    startTime: startElement.value,
                    endTime: endElement.value,
                    index: slotIndex
                });
            }

            slotIndex++;
            startElement = document.getElementById(`${prefix}-${day}-start-${slotIndex}`);
            endElement = document.getElementById(`${prefix}-${day}-end-${slotIndex}`);
        }

        // Sort slots by start time to ensure chronological order
        return slots.sort((a, b) => a.startTime.localeCompare(b.startTime));
    }

    /**
     * Get the minimum available start time for a new slot
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @returns {string|null} Minimum start time or null if no slots exist
     */
    getMinimumStartTimeForNewSlot(prefix, day) {
        const existingSlots = this.getDayTimeSlots(prefix, day);

        if (existingSlots.length === 0) {
            return null; // No existing slots, any time is valid
        }

        // Find the latest end time among all existing slots
        const latestEndTime = existingSlots.reduce((latest, slot) => {
            return slot.endTime > latest ? slot.endTime : latest;
        }, existingSlots[0].endTime);

        // Find the next available time slot after the latest end time
        const nextTimeIndex = this.timeConfig.options.findIndex(opt => opt.value > latestEndTime);

        return nextTimeIndex >= 0 ? this.timeConfig.options[nextTimeIndex].value : null;
    }

    /**
     * Get available start time options for a new slot
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @returns {Array} Array of available time options
     */
    getAvailableStartTimesForNewSlot(prefix, day) {
        const minStartTime = this.getMinimumStartTimeForNewSlot(prefix, day);

        if (minStartTime === null) {
            return this.timeConfig.options; // No restrictions if no existing slots
        }

        return this.timeConfig.options.filter(opt => opt.value >= minStartTime);
    }

    /**
     * Update all subsequent time slots when an earlier slot changes
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @param {number} changedSlotIndex - Index of the slot that changed
     */
    updateSubsequentSlots(prefix, day, changedSlotIndex) {
        const allSlots = this.getDayTimeSlots(prefix, day);

        // Find slots that come after the changed slot chronologically
        const changedSlot = allSlots.find(slot => slot.index === changedSlotIndex);
        if (!changedSlot) return;

        const subsequentSlots = allSlots.filter(slot =>
            slot.index > changedSlotIndex && slot.startTime >= changedSlot.endTime
        );

        // Update each subsequent slot's available options
        subsequentSlots.forEach((slot, index) => {
            const previousSlot = index === 0 ? changedSlot : subsequentSlots[index - 1];

            // Update start time options
            const availableStartTimes = this.timeConfig.options.filter(
                opt => opt.value >= previousSlot.endTime
            );

            if (availableStartTimes.length > 0) {
                // Preserve current value if still valid, otherwise select first available
                const currentStartValue = slot.startElement.value;
                const isCurrentValid = availableStartTimes.some(opt => opt.value === currentStartValue);
                const newStartValue = isCurrentValid ? currentStartValue : availableStartTimes[0].value;

                this.updateSelectOptions(slot.startElement, availableStartTimes, newStartValue);

                // Update end time options based on new start time
                const availableEndTimes = this.timeConfig.options.filter(
                    opt => opt.value > newStartValue
                );

                if (availableEndTimes.length > 0) {
                    const currentEndValue = slot.endElement.value;
                    const isCurrentEndValid = availableEndTimes.some(opt => opt.value === currentEndValue);
                    const newEndValue = isCurrentEndValid ? currentEndValue : availableEndTimes[0].value;

                    this.updateSelectOptions(slot.endElement, availableEndTimes, newEndValue);
                }
            }
        });
    }

    /**
     * Check if there are any available time slots remaining for the day
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @returns {boolean} True if more slots can be added
     */
    canAddMoreSlots(prefix, day) {
        const availableStartTimes = this.getAvailableStartTimesForNewSlot(prefix, day);

        // Need at least 2 time options (start and end) to create a valid slot
        return availableStartTimes.length >= 2;
    }

    /**
     * Update the state of the add button for a specific day
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     */
    updateAddButtonState(prefix, day) {
        const addButton = document.querySelector(`[data-day="${prefix}-${day}"] .add-hours-btn`);
        if (!addButton) return;

        const canAdd = this.canAddMoreSlots(prefix, day);
        const dayToggle = document.getElementById(`${prefix}-${day}-toggle`);
        const isDayEnabled = dayToggle && dayToggle.checked;

        // Enable button only if day is enabled and more slots can be added
        addButton.disabled = !isDayEnabled || !canAdd;

        // Update button appearance and tooltip
        if (!canAdd) {
            addButton.setAttribute('title', 'No more time slots available for this day');
            addButton.style.opacity = '0.5';
            addButton.style.cursor = 'not-allowed';
        } else if (!isDayEnabled) {
            addButton.setAttribute('title', 'Enable this day to add time slots');
            addButton.style.opacity = '0.5';
            addButton.style.cursor = 'not-allowed';
        } else {
            addButton.setAttribute('title', 'Add another time slot');
            addButton.style.opacity = '1';
            addButton.style.cursor = 'pointer';
        }
    }

    /**
     * Update all add button states for all days
     */
    updateAllAddButtonStates() {
        const prefixes = ['biz', 'scan'];
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

        prefixes.forEach(prefix => {
            days.forEach(day => {
                this.updateAddButtonState(prefix, day);
            });
        });
    }

    /**
     * Dispatch custom event for time changes following the pattern from custom-select.js
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     */
    dispatchTimeChangeEvent(prefix, day) {
        const daySlots = this.getDayTimeSlots(prefix, day);
        const dayToggle = document.getElementById(`${prefix}-${day}-toggle`);
        const isEnabled = dayToggle && dayToggle.checked;

        const event = new CustomEvent('timeSlotChange', {
            detail: {
                prefix: prefix,
                day: day,
                isEnabled: isEnabled,
                slots: daySlots.map(slot => ({
                    startTime: slot.startTime,
                    endTime: slot.endTime,
                    index: slot.index
                })),
                canAddMore: this.canAddMoreSlots(prefix, day),
                timestamp: new Date().toISOString()
            },
            bubbles: true
        });

        // Dispatch from the day container or document if container not found
        const dayContainer = document.querySelector(`[data-day="${prefix}-${day}"]`);
        const target = dayContainer || document;
        target.dispatchEvent(event);
    }

    /**
     * Dispatch comprehensive business hours change event
     * This follows the existing pattern but includes smart filtering information
     */
    dispatchSmartBusinessHoursChangeEvent() {
        const allData = this.getAllHoursData();

        // Add smart filtering metadata
        const smartFilteringData = {
            businessHours: {},
            scannerHours: {}
        };

        // Collect smart filtering data for each day
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

        ['biz', 'scan'].forEach(prefix => {
            const hoursType = prefix === 'biz' ? 'businessHours' : 'scannerHours';
            smartFilteringData[hoursType] = {};

            days.forEach(day => {
                const dayToggle = document.getElementById(`${prefix}-${day}-toggle`);
                const isEnabled = dayToggle && dayToggle.checked;
                const slots = this.getDayTimeSlots(prefix, day);
                const canAddMore = this.canAddMoreSlots(prefix, day);
                const availableStartTimes = this.getAvailableStartTimesForNewSlot(prefix, day);

                smartFilteringData[hoursType][day] = {
                    isEnabled,
                    slotsCount: slots.length,
                    canAddMore,
                    availableStartTimesCount: availableStartTimes.length,
                    slots: slots.map(slot => ({
                        startTime: slot.startTime,
                        endTime: slot.endTime,
                        index: slot.index,
                        validation: this.validateTimeSlot(prefix, day, slot.index)
                    }))
                };
            });
        });

        const event = new CustomEvent('smartBusinessHoursChange', {
            detail: {
                allData: allData,
                smartFiltering: smartFilteringData,
                timestamp: new Date().toISOString(),
                version: '2.0' // Version to distinguish from basic events
            },
            bubbles: true
        });

        document.dispatchEvent(event);
    }

    /**
     * Validate time slot and show appropriate warnings
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @param {number} slotIndex - Index of the slot to validate
     * @returns {Object} Validation result with isValid and message
     */
    validateTimeSlot(prefix, day, slotIndex) {
        const startElement = slotIndex === 0
            ? document.getElementById(`${prefix}-${day}-start`)
            : document.getElementById(`${prefix}-${day}-start-${slotIndex}`);
        const endElement = slotIndex === 0
            ? document.getElementById(`${prefix}-${day}-end`)
            : document.getElementById(`${prefix}-${day}-end-${slotIndex}`);

        if (!startElement || !endElement) {
            return { isValid: false, message: 'Time slot elements not found' };
        }

        const startTime = startElement.value;
        const endTime = endElement.value;

        if (!startTime || !endTime) {
            return { isValid: false, message: 'Please select both start and end times' };
        }

        if (startTime >= endTime) {
            return { isValid: false, message: 'End time must be after start time' };
        }

        // Check for overlaps with other slots
        const allSlots = this.getDayTimeSlots(prefix, day);
        const otherSlots = allSlots.filter(slot => slot.index !== slotIndex);

        for (const otherSlot of otherSlots) {
            // Check if current slot overlaps with any other slot
            if ((startTime >= otherSlot.startTime && startTime < otherSlot.endTime) ||
                (endTime > otherSlot.startTime && endTime <= otherSlot.endTime) ||
                (startTime <= otherSlot.startTime && endTime >= otherSlot.endTime)) {
                return { isValid: false, message: 'Time slot overlaps with another slot' };
            }
        }

        return { isValid: true, message: '' };
    }

    /**
     * Show inline warning message for a time slot
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @param {number} slotIndex - Index of the slot
     * @param {string} message - Warning message to display
     */
    showTimeSlotWarning(prefix, day, slotIndex, message) {
        // Find the time inputs container
        const timeInputsContainer = document.querySelector(`[data-day="${prefix}-${day}"] .time-inputs-container`);
        if (!timeInputsContainer) return;

        // Remove existing warning
        this.clearTimeSlotWarning(prefix, day, slotIndex);

        // Create warning element
        const warningElement = document.createElement('div');
        warningElement.className = 'time-slot-warning';
        warningElement.setAttribute('data-slot-index', slotIndex.toString());
        warningElement.innerHTML = `
            <span class="warning-icon">⚠️</span>
            <span class="warning-text">${message}</span>
        `;

        // Add warning styles
        warningElement.style.cssText = `
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background-color: #fef3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            color: #856404;
            font-size: 14px;
            margin-top: 4px;
            animation: slideIn 0.3s ease-out;
        `;

        // Find the appropriate container to append the warning
        if (slotIndex === 0) {
            const defaultTimeInputs = timeInputsContainer.querySelector('.time-inputs-default');
            if (defaultTimeInputs) {
                defaultTimeInputs.appendChild(warningElement);
            }
        } else {
            const additionalSlots = timeInputsContainer.querySelectorAll('.additional-time-slot');
            const targetSlot = Array.from(additionalSlots).find(slot => {
                const select = slot.querySelector('.time-select');
                return select && select.id.includes(`-${slotIndex}`);
            });
            if (targetSlot) {
                targetSlot.appendChild(warningElement);
            }
        }
    }

    /**
     * Clear warning message for a time slot
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @param {number} slotIndex - Index of the slot
     */
    clearTimeSlotWarning(prefix, day, slotIndex) {
        const timeInputsContainer = document.querySelector(`[data-day="${prefix}-${day}"] .time-inputs-container`);
        if (!timeInputsContainer) return;

        const existingWarning = timeInputsContainer.querySelector(
            `.time-slot-warning[data-slot-index="${slotIndex}"]`
        );
        if (existingWarning) {
            existingWarning.remove();
        }
    }

    /**
     * Auto-adjust invalid time selections
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day name
     * @param {number} slotIndex - Index of the slot
     * @returns {boolean} True if adjustment was made
     */
    autoAdjustTimeSlot(prefix, day, slotIndex) {
        const startElement = slotIndex === 0
            ? document.getElementById(`${prefix}-${day}-start`)
            : document.getElementById(`${prefix}-${day}-start-${slotIndex}`);
        const endElement = slotIndex === 0
            ? document.getElementById(`${prefix}-${day}-end`)
            : document.getElementById(`${prefix}-${day}-end-${slotIndex}`);

        if (!startElement || !endElement) return false;

        const startTime = startElement.value;
        const endTime = endElement.value;

        // If end time is not after start time, auto-adjust end time
        if (startTime && endTime && startTime >= endTime) {
            const availableEndTimes = this.timeConfig.options.filter(
                opt => opt.value > startTime
            );

            if (availableEndTimes.length > 0) {
                // Set end time to the next available slot (minimum 30 minutes after start)
                const newEndTime = availableEndTimes[0].value;
                this.updateSelectOptions(endElement, availableEndTimes, newEndTime);

                // Show a brief success message
                this.showTimeSlotWarning(prefix, day, slotIndex,
                    `End time auto-adjusted to ${this.timeConfig.options.find(opt => opt.value === newEndTime)?.label}`);

                // Clear the message after 3 seconds
                setTimeout(() => {
                    this.clearTimeSlotWarning(prefix, day, slotIndex);
                }, 3000);

                return true;
            }
        }

        return false;
    }

    /**
     * Initialize Facility Info Step
     * Sets up time options and "same as business hours" functionality
     */
    initializeFacilityInfoStep() {
        this.generateHoursSection("business");
        this.generateHoursSection("scanner");

        // Set up event listeners after generating sections
        this.setupHoursEventListeners();

        // Same as Business Hours toggle
        const { sameHoursCheck, scannerHoursSection, scannerHoursContainer } = this.elements;
        if (sameHoursCheck) {
            if (!sameHoursCheck.checked) {
                sameHoursCheck.checked = true;
                this.hideScannerHours(scannerHoursSection, scannerHoursContainer);
            } else if (sameHoursCheck.checked) {
                this.hideScannerHours(scannerHoursSection, scannerHoursContainer);
            }

            sameHoursCheck.addEventListener("change", () => {
                if (sameHoursCheck.checked) {
                    this.hideScannerHours(scannerHoursSection, scannerHoursContainer);
                } else {
                    this.showScannerHours(scannerHoursSection, scannerHoursContainer);

                    setTimeout(() => {
                        this.generateHoursSection("scanner");
                        this.setupHoursEventListeners();
                        this.saveCurrentData();
                    }, 200);
                }
            });
        }
    }

    /**
     * Generate dynamic hours section
     * @param {string} type - Either 'business' or 'scanner'
     */
    generateHoursSection(type) {
        const prefix = type === "business" ? "biz" : "scan";
        const containerId =
            type === "business" ? "business-hours" : "scanner-hours";

        const container = document.getElementById(containerId);
        if (!container) return;

        // Clear existing content
        container.innerHTML = "";

        // Generate each day row
        this.DAYS.forEach((dayConfig) => {
            const dayRow = this.createDayRow(prefix, dayConfig.key, dayConfig.name);
            container.appendChild(dayRow);
        });
    }

    /**
     * Create a single day row
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day key (e.g., 'mon', 'tue')
     * @param {string} label - Display label for the day
     */
    createDayRow(prefix, day, label) {
        const row = document.createElement("div");
        row.className = "bd-hours-row";

        const dayConfig = this.DAYS.find(d => d.key === day);
        const isChecked = dayConfig ? dayConfig.isWeekday : false;

        row.innerHTML = `<div class="toggle-switch-container"><div class="toggle-switch"><input type="checkbox" id="${prefix}-${day}" name="${prefix}-${day}" class="day-toggle" data-day="${day}" ${isChecked ? "checked" : ""}/><label for="${prefix}-${day}" class="toggle-slider"></label></div><label for="${prefix}-${day}">${label.slice(0, 3)}</label></div><div class="time-inputs-container"><div class="time-inputs-default">${isChecked ? this.createTimeInputsHTML(prefix, day) : ""}${!isChecked ? '<span class="unavailable-text">Unavailable</span>' : ""}${isChecked ? `<button class="add-hours-btn plus-icon" type="button"></button>` : ""}</div></div>`;
        return row;
    }

    /**
     * Create time inputs HTML
     */
    createTimeInputsHTML(prefix, day) {
        const timeOptions = this.getTimeOptions();

        return `<div class="time-inputs"><select id="${prefix}-${day}-start" name="${prefix}-${day}-start" class="time-select">${timeOptions}</select><span>-</span><select id="${prefix}-${day}-end" name="${prefix}-${day}-end" class="time-select">${timeOptions}</select></div>`;
    }

    /**
     * Get time options HTML
     */
    getTimeOptions() {
        return this.generateTimeOptionsHTML(this.timeConfig.options);
    }

    /**
     * Setup event listeners for hours sections
     */
    setupHoursEventListeners() {
        const dayToggles = document.querySelectorAll(".day-toggle");
        dayToggles.forEach((toggle) => {
            // Check if event listener already exists
            if (!toggle.hasAttribute("data-listener-added")) {
                toggle.addEventListener("change", () => {
                    this.toggleDayAvailability(toggle);
                    this.saveCurrentData();
                });
                toggle.setAttribute("data-listener-added", "true");
            }

            // Initialize toggle state on load
            this.toggleDayAvailability(toggle);
        });

        // Add time slot buttons - only add listeners to buttons that don't have them
        const addHoursBtns = document.querySelectorAll(
            ".add-hours-btn:not([data-listener-added])"
        );
        addHoursBtns.forEach((btn) => {
            btn.addEventListener("click", this.addTimeSlot.bind(this));
            btn.setAttribute("data-listener-added", "true");
        });

        // Add smart filtering and auto-save functionality to all existing time select dropdowns
        const timeSelects = document.querySelectorAll(
            ".time-select:not([data-listener-added])"
        );
        timeSelects.forEach((select) => {
            select.addEventListener("change", () => {
                // Parse the select ID to get prefix, day, and slot info
                const idParts = select.id.split('-');
                const prefix = idParts[0]; // 'biz' or 'scan'
                const day = idParts[1]; // day name

                // If start time changed, update corresponding end time options with smart filtering
                const startMatch = select.id.match(/^(.+)-start(-\d*)?$/);
                const endMatch = select.id.match(/^(.+)-end(-\d*)?$/);

                if (startMatch) {
                    const base = startMatch[1];
                    const suffix = startMatch[2] || "";
                    const slotIndex = suffix ? parseInt(suffix.substring(1)) : 0;
                    const endSelect = document.getElementById(`${base}-end${suffix}`);

                    if (endSelect) {
                        // Get current end value to preserve if still valid
                        const currentEndValue = endSelect.value;

                        // Filter options to be greater than selected start value
                        const filteredOptions = this.timeConfig.options.filter(
                            (opt) => opt.value > select.value
                        );

                        // Determine which value to select
                        let valueToSelect = null;
                        if (
                            currentEndValue &&
                            filteredOptions.some((opt) => opt.value === currentEndValue)
                        ) {
                            valueToSelect = currentEndValue;
                        } else if (filteredOptions.length > 0) {
                            valueToSelect = filteredOptions[0].value;
                        }

                        // Clear existing options and generate new ones with proper selection
                        this.updateSelectOptions(endSelect, filteredOptions, valueToSelect);

                        // Update subsequent slots if this is not the last slot
                        this.updateSubsequentSlots(prefix, day, slotIndex);

                        // Validate and show warnings/auto-adjust if needed
                        const validation = this.validateTimeSlot(prefix, day, slotIndex);
                        if (!validation.isValid) {
                            // Try auto-adjustment first
                            const adjusted = this.autoAdjustTimeSlot(prefix, day, slotIndex);
                            if (!adjusted) {
                                this.showTimeSlotWarning(prefix, day, slotIndex, validation.message);
                            }
                        } else {
                            this.clearTimeSlotWarning(prefix, day, slotIndex);
                        }
                    }
                } else if (endMatch) {
                    // If end time changed, update subsequent slots
                    const suffix = endMatch[2] || "";
                    const slotIndex = suffix ? parseInt(suffix.substring(1)) : 0;

                    this.updateSubsequentSlots(prefix, day, slotIndex);

                    // Validate and show warnings for end time changes
                    const validation = this.validateTimeSlot(prefix, day, slotIndex);
                    if (!validation.isValid) {
                        this.showTimeSlotWarning(prefix, day, slotIndex, validation.message);
                    } else {
                        this.clearTimeSlotWarning(prefix, day, slotIndex);
                    }
                }

                // Update add button state
                this.updateAddButtonState(prefix, day);

                // Dispatch custom event for time change
                this.dispatchTimeChangeEvent(prefix, day);

                this.saveCurrentData();
            });
            select.setAttribute("data-listener-added", "true");
        });

        // Initialize end time options based on current start values
        timeSelects.forEach((select) => {
            if (select.id.match(/^(.+)-start(-\d*)?$/)) {
                // Only initialize if not already initialized
                if (!select.hasAttribute("data-initialized")) {
                    select.dispatchEvent(new Event("change"));
                    select.setAttribute("data-initialized", "true");
                }
            }
        });
    }

    /**
     * Toggle day availability
     * @param {HTMLInputElement} toggle - The day toggle checkbox
     */
    toggleDayAvailability(toggle) {
        const row = toggle.closest(".bd-hours-row");
        const timeInputsContainer = row.querySelector(".time-inputs-container");
        const timeInputsDefault = timeInputsContainer.querySelector(
            ".time-inputs-default"
        );
        const timeInputs = timeInputsDefault.querySelector(".time-inputs");
        const addBtn = timeInputsDefault.querySelector(".add-hours-btn.plus-icon");
        const unavailableText =
            timeInputsDefault.querySelector(".unavailable-text");
        const additionalTimeSlots = timeInputsContainer.querySelectorAll(
            ".additional-time-slot"
        );

        if (toggle.checked) {
            if (!timeInputs) {
                this.createTimeInputs(row, toggle);
            } else {
                timeInputs.style.display = "flex";
                if (addBtn) addBtn.style.display = "flex";
            }

            if (unavailableText) {
                unavailableText.style.display = "none";
            }
        } else {
            if (timeInputs) {
                timeInputs.style.display = "none";
            }
            if (addBtn) {
                addBtn.style.display = "none";
            }

            additionalTimeSlots.forEach((slot) => {
                slot.remove();
            });

            if (unavailableText) {
                unavailableText.style.display = "inline";
            } else {
                const newUnavailableText = document.createElement("span");
                newUnavailableText.className = "unavailable-text";
                newUnavailableText.textContent = "Unavailable";
                timeInputsDefault.appendChild(newUnavailableText);
            }
        }

        // Update add button state for this day
        const prefix = toggle.id.split("-")[0]; // 'biz' or 'scan'
        const day = toggle.id.split("-")[1]; // day name
        this.updateAddButtonState(prefix, day);

        // Dispatch specific event based on the type (business or scanner)
        if (prefix === "biz") {
            this.dispatchBusinessHoursChangeEvent();
        } else if (prefix === "scan") {
            this.dispatchScannerHoursChangeEvent();
        }
    }

    /**
     * Create time inputs for a day
     * @param {HTMLElement} row - The day row element
     * @param {HTMLInputElement} toggle - The day toggle checkbox
     */
    createTimeInputs(row, toggle) {
        const prefix = toggle.id.split("-")[0]; // 'biz' or 'scan'
        const day = toggle.id.split("-")[1]; // day name
        const timeInputsContainer = row.querySelector(".time-inputs-container");
        const timeInputsDefault = timeInputsContainer.querySelector(
            ".time-inputs-default"
        );

        // Remove unavailable text if it exists
        const unavailableText =
            timeInputsDefault.querySelector(".unavailable-text");
        if (unavailableText) {
            unavailableText.remove();
        }

        const newTimeInputs = document.createElement("div");
        newTimeInputs.className = "time-inputs";

        // Generate options using global configuration
        const timeOptions = this.getTimeOptions();

        newTimeInputs.innerHTML = `
            <select id="${prefix}-${day}-start" name="${prefix}-${day}-start" class="time-select">
                ${timeOptions}
            </select>
            <span>-</span>
            <select id="${prefix}-${day}-end" name="${prefix}-${day}-end" class="time-select">
                ${timeOptions}
            </select>
        `;

        // Add to time-inputs-default container
        timeInputsDefault.appendChild(newTimeInputs);

        // Create add button
        const newAddBtn = document.createElement("button");
        newAddBtn.className = "add-hours-btn plus-icon";
        newAddBtn.type = "button";

        newAddBtn.addEventListener("click", this.addTimeSlot.bind(this));
        newAddBtn.setAttribute("data-listener-added", "true");
        timeInputsDefault.appendChild(newAddBtn);
    }

    /**
     * Add time slot functionality
     * @param {Event} e - The click event
     */
    addTimeSlot(e) {
        const row = e.target.closest(".bd-hours-row");
        const timeInputsContainer = row.querySelector(".time-inputs-container");

        // Get prefix and day from the existing toggle
        const toggle = row.querySelector(".day-toggle");
        const prefix = toggle.id.split("-")[0]; // 'biz' or 'scan'
        const day = toggle.id.split("-")[1]; // day name

        // Generate unique index for additional time slots
        const existingSlots = timeInputsContainer.querySelectorAll(
            ".additional-time-slot"
        );
        const slotIndex = existingSlots.length + 1;

        // Create a new time slot div
        const newTimeSlot = document.createElement("div");
        newTimeSlot.className = "additional-time-slot";

        // Create time inputs with proper IDs and names using global configuration
        const timeInputs = document.createElement("div");
        timeInputs.className = "time-inputs";

        // Generate smart time options based on existing slots
        const availableStartTimes = this.getAvailableStartTimesForNewSlot(prefix, day);
        const defaultStartTime = availableStartTimes.length > 0 ? availableStartTimes[0].value : "12:00";

        const startOptions = this.generateTimeOptionsHTML(
            availableStartTimes,
            defaultStartTime
        );

        // Filter end options to be greater than the default start time
        const filteredEndOptions = this.timeConfig.options.filter(
            (opt) => opt.value > defaultStartTime
        );
        const defaultEndTime = filteredEndOptions.length > 0 ? filteredEndOptions[0].value : "18:00";

        const endOptions = this.generateTimeOptionsHTML(
            filteredEndOptions,
            defaultEndTime
        );

        timeInputs.innerHTML = `
            <select id="${prefix}-${day}-start-${slotIndex}" name="${prefix}-${day}-start-${slotIndex}" class="time-select">
                ${startOptions}
            </select>
            <span>-</span>
            <select id="${prefix}-${day}-end-${slotIndex}" name="${prefix}-${day}-end-${slotIndex}" class="time-select">
                ${endOptions}
            </select>
        `;

        // Create remove button
        const removeBtn = document.createElement("button");
        removeBtn.className = "add-hours-btn remove-hours-btn";
        removeBtn.type = "button";
        removeBtn.style.backgroundColor = "#EBEBEB";
        removeBtn.setAttribute("aria-label", "Remove time slot");

        removeBtn.addEventListener("click", () => {
            this.removeTimeSlot(newTimeSlot);
            // Auto-save data after removing time slot
            setTimeout(() => {
                this.saveCurrentData();
            }, 350); // Slight delay to ensure removal animation completes
        });

        // Add time inputs and remove button to the new time slot
        newTimeSlot.appendChild(timeInputs);
        newTimeSlot.appendChild(removeBtn);

        // Set initial state for animation
        newTimeSlot.style.opacity = "0";
        newTimeSlot.style.transform = "translateY(-10px) scale(0.95)";
        newTimeSlot.style.maxHeight = "0px";
        newTimeSlot.style.overflow = "hidden";
        newTimeSlot.style.transition = "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)";

        // Add the additional time slot at the END (append to container)
        timeInputsContainer.appendChild(newTimeSlot);

        // Trigger animation on next frame
        requestAnimationFrame(() => {
            newTimeSlot.style.opacity = "1";
            newTimeSlot.style.transform = "translateY(0) scale(1)";
            newTimeSlot.style.maxHeight = "60px"; // Adjust based on actual content height

            // Remove maxHeight after animation completes
            setTimeout(() => {
                newTimeSlot.style.maxHeight = "";
                newTimeSlot.style.overflow = "";

                // Add enhanced smart filtering event listeners to the new time select dropdowns
                const timeSelects = newTimeSlot.querySelectorAll(".time-select");
                timeSelects.forEach((select) => {
                    select.addEventListener("change", () => {
                        // Parse the select ID to get prefix, day, and slot info
                        const idParts = select.id.split('-');
                        const selectPrefix = idParts[0]; // 'biz' or 'scan'
                        const selectDay = idParts[1]; // day name

                        // If start time changed, update corresponding end time options with smart filtering
                        const startMatch = select.id.match(/^(.+)-start(-\d*)?$/);
                        const endMatch = select.id.match(/^(.+)-end(-\d*)?$/);

                        if (startMatch) {
                            const base = startMatch[1];
                            const suffix = startMatch[2] || "";
                            const slotIndex = suffix ? parseInt(suffix.substring(1)) : 0;
                            const endSelect = document.getElementById(`${base}-end${suffix}`);

                            if (endSelect) {
                                // Filter options to be greater than selected start value
                                const filteredOptions = this.timeConfig.options.filter(
                                    (opt) => opt.value > select.value
                                );

                                // Determine which value to select
                                let valueToSelect = null;
                                const currentEndValue = endSelect.value;
                                if (
                                    currentEndValue &&
                                    filteredOptions.some((opt) => opt.value === currentEndValue)
                                ) {
                                    valueToSelect = currentEndValue;
                                } else if (filteredOptions.length > 0) {
                                    valueToSelect = filteredOptions[0].value;
                                }

                                this.updateSelectOptions(endSelect, filteredOptions, valueToSelect);

                                // Update subsequent slots if this is not the last slot
                                this.updateSubsequentSlots(selectPrefix, selectDay, slotIndex);
                            }
                        } else if (endMatch) {
                            // If end time changed, update subsequent slots
                            const suffix = endMatch[2] || "";
                            const slotIndex = suffix ? parseInt(suffix.substring(1)) : 0;

                            this.updateSubsequentSlots(selectPrefix, selectDay, slotIndex);
                        }

                        // Update add button state
                        this.updateAddButtonState(selectPrefix, selectDay);

                        // Dispatch custom event for time change
                        this.dispatchTimeChangeEvent(selectPrefix, selectDay);

                        this.saveCurrentData();
                    });
                    select.setAttribute("data-listener-added", "true");
                });

                // Initialize end time options based on current start value
                const startSelect = newTimeSlot.querySelector(
                    `[id$="-start-${slotIndex}"]`
                );
                if (startSelect) {
                    startSelect.dispatchEvent(new Event("change"));
                }

                // Update add button state after adding the slot
                this.updateAddButtonState(prefix, day);

                // Auto-save data after adding time slot
                this.saveCurrentData();
            }, 300);
        });
    }

    /**
     * Remove time slot functionality with smooth animation
     * @param {HTMLElement} timeSlotElement - The time slot element to remove
     */
    removeTimeSlot(timeSlotElement) {
        if (timeSlotElement && timeSlotElement.parentNode) {
            // Extract prefix and day from the time slot's select elements
            const selectElement = timeSlotElement.querySelector('.time-select');
            let prefix = null;
            let day = null;

            if (selectElement) {
                const idParts = selectElement.id.split('-');
                prefix = idParts[0]; // 'biz' or 'scan'
                day = idParts[1]; // day name
            }

            // Set up animation styles
            timeSlotElement.style.transition =
                "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)";
            timeSlotElement.style.transformOrigin = "center";

            // Get current height for smooth collapse animation
            const currentHeight = timeSlotElement.offsetHeight;
            timeSlotElement.style.height = currentHeight + "px";

            // Force a reflow to ensure the height is set
            timeSlotElement.offsetHeight;

            // Start the removal animation
            timeSlotElement.style.opacity = "0";
            timeSlotElement.style.transform = "translateY(-10px) scale(0.95)";
            timeSlotElement.style.height = "0px";
            timeSlotElement.style.marginBottom = "0px";
            timeSlotElement.style.paddingTop = "0px";
            timeSlotElement.style.paddingBottom = "0px";

            // Remove element after animation completes
            setTimeout(() => {
                if (timeSlotElement && timeSlotElement.parentNode) {
                    timeSlotElement.parentNode.removeChild(timeSlotElement);

                    // Update add button state after removal
                    if (prefix && day) {
                        this.updateAddButtonState(prefix, day);
                        this.dispatchTimeChangeEvent(prefix, day);
                    }
                }
            }, 300);
        }
    }

    /**
     * Hide scanner hours with smooth animation
     * @param {HTMLElement} scannerHoursSection - Scanner hours section element
     * @param {HTMLElement} scannerHoursContainer - Scanner hours container element
     */
    hideScannerHours(scannerHoursSection, scannerHoursContainer) {
        if (scannerHoursSection) {
            scannerHoursSection.classList.add("hiding");
            scannerHoursSection.classList.remove("showing");
        }

        if (scannerHoursContainer) {
            scannerHoursContainer.classList.add("sync-enabled");
            scannerHoursContainer.querySelector("label").classList.add("checked");
        }
    }

    /**
     * Show scanner hours with smooth animation
     * @param {HTMLElement} scannerHoursSection - Scanner hours section element
     * @param {HTMLElement} scannerHoursContainer - Scanner hours container element
     */
    showScannerHours(scannerHoursSection, scannerHoursContainer) {
        if (scannerHoursContainer) {
            scannerHoursContainer.classList.remove("sync-enabled");
            scannerHoursContainer.querySelector("label").classList.remove("checked");
        }

        if (scannerHoursSection) {
            scannerHoursSection.classList.remove("hiding");
            scannerHoursSection.classList.add("showing");
        }
    }

    /**
     * Save current data and dispatch change event
     * This method can be overridden or provided by the main form class
     */
    saveCurrentData() {
        this.dispatchChangeEvent();
        this.dispatchSmartBusinessHoursChangeEvent();
    }

    /**
     * Dispatch custom change event with business hours data
     * Similar to custom-select.js event system
     */
    dispatchChangeEvent() {
        const businessHoursData = this.getHoursData("biz");
        const scannerHoursData = this.getHoursData("scan");

        const event = new CustomEvent("businessHoursChange", {
            detail: {
                businessHours: businessHoursData,
                scannerHours: scannerHoursData,
                allData: this.getAllHoursData(),
                timestamp: new Date().toISOString(),
            },
            bubbles: true,
            cancelable: true,
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Dispatch specific event for business hours only
     */
    dispatchBusinessHoursChangeEvent() {
        const businessHoursData = this.getHoursData("biz");

        const event = new CustomEvent("businessHoursOnlyChange", {
            detail: {
                businessHours: businessHoursData,
                type: "business",
                timestamp: new Date().toISOString(),
            },
            bubbles: true,
            cancelable: true,
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Dispatch specific event for scanner hours only
     */
    dispatchScannerHoursChangeEvent() {
        const scannerHoursData = this.getHoursData("scan");

        const event = new CustomEvent("scannerHoursOnlyChange", {
            detail: {
                scannerHours: scannerHoursData,
                type: "scanner",
                timestamp: new Date().toISOString(),
            },
            bubbles: true,
            cancelable: true,
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Get all hours data in a comprehensive format
     * @returns {Object} - Complete hours data structure
     */
    getAllHoursData() {
        return {
            business: this.getHoursData("biz"),
            scanner: this.getHoursData("scan"),
            sameHours: this.isSameHoursEnabled(),
        };
    }

    /**
     * Check if "same hours" option is enabled
     * @returns {boolean} - True if same hours is checked
     */
    isSameHoursEnabled() {
        const sameHoursCheck = this.element.sameHoursCheck;
        return sameHoursCheck ? sameHoursCheck.checked : false;
    }

    /**
     * Get business hours data in structured format
     * @param {string} prefix - Either 'biz' or 'scan'
     * @returns {Array} - Array of hours data for each day
     */
    getHoursData(prefix) {
        const days = this.DAYS_CONFIG.KEYS;
        const dayNames = this.DAYS_CONFIG.NAMES;
        const hoursData = [];

        days.forEach((day, index) => {
            const toggle = document.getElementById(`${prefix}-${day}`);

            // Determine if day is closed (unavailable)
            const isClosed = !toggle || !toggle.checked;

            const dayData = {
                day: dayNames[index],
                is_closed: isClosed,
                is_open_24: false,
                hours: [],
            };

            // Only process hours if day is enabled (not closed)
            if (!isClosed) {
                const startTime = document.getElementById(`${prefix}-${day}-start`);
                const endTime = document.getElementById(`${prefix}-${day}-end`);

                if (startTime && endTime && startTime.value && endTime.value) {
                    dayData.hours.push({
                        open_time: startTime.value,
                        close_time: endTime.value,
                    });
                }

                // Get additional time slots
                let slotIndex = 1;
                let startTimeSlot = document.getElementById(
                    `${prefix}-${day}-start-${slotIndex}`
                );
                let endTimeSlot = document.getElementById(
                    `${prefix}-${day}-end-${slotIndex}`
                );

                while (
                    startTimeSlot &&
                    endTimeSlot &&
                    startTimeSlot.value &&
                    endTimeSlot.value
                ) {
                    dayData.hours.push({
                        open_time: startTimeSlot.value,
                        close_time: endTimeSlot.value,
                    });
                    slotIndex++;
                    startTimeSlot = document.getElementById(
                        `${prefix}-${day}-start-${slotIndex}`
                    );
                    endTimeSlot = document.getElementById(
                        `${prefix}-${day}-end-${slotIndex}`
                    );
                }
            }

            // Always add all days (both open and closed)
            hoursData.push(dayData);
        });

        return hoursData;
    }

    /**
     * Initialize the business hours manager
     * This should be called after the DOM is ready
     */
    init() {
        if (this.initialized) {
            return;
        }

        this.initializeFacilityInfoStep();
        this.initialized = true;
    }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
    if (typeof window !== "undefined") {
        window.BusinessHoursManager = new BusinessHoursManager();
        window.BusinessHoursManager.init();
    }
});
