# Business Hours Manager - Updated with Custom Events

## Overview

The BusinessHoursManager has been updated to expose custom events similar to the custom-select.js component. This allows external code to listen for changes and access business hours data in real-time.

## Key Features Added

### 🎯 Custom Events System
- **`businessHoursChange`** - Dispatched on any change (business or scanner hours)
- **`businessHoursOnlyChange`** - Dispatched only when business hours change
- **`scannerHoursOnlyChange`** - Dispatched only when scanner hours change

### 🔧 Static Methods for Data Access
- `BusinessHoursManager.getBusinessHours()` - Get business hours data
- `BusinessHoursManager.getScannerHours()` - Get scanner hours data  
- `BusinessHoursManager.getAllData()` - Get complete data structure
- `BusinessHoursManager.getInstance()` - Get current instance

### 📊 Rich Event Data
Each event includes:
- Structured hours data for each day
- Validation status
- Timestamp
- Complete data context

## Quick Start

### 1. Listen for Changes
```javascript
document.addEventListener('businessHoursChange', function(event) {
  console.log('Hours changed:', event.detail);
  
  // Access the data
  const { businessHours, scannerHours, allData } = event.detail;
  
  // Update your UI
  updateHoursDisplay(businessHours);
});
```

### 2. Get Data Programmatically
```javascript
// Get current data anytime
const businessHours = BusinessHoursManager.getBusinessHours();
const scannerHours = BusinessHoursManager.getScannerHours();
const allData = BusinessHoursManager.getAllData();
```

### 3. Real-time Rendering
```javascript
function renderHours(hoursData) {
  hoursData.forEach(day => {
    if (day.is_closed) {
      console.log(`${day.day}: Closed`);
    } else {
      day.hours.forEach(slot => {
        console.log(`${day.day}: ${slot.open_time} - ${slot.close_time}`);
      });
    }
  });
}

document.addEventListener('businessHoursChange', function(event) {
  renderHours(event.detail.businessHours);
});
```

## Files

- **`business-hours-manager.js`** - Main component with custom events
- **`example-usage.html`** - Complete working example with event logging
- **`EVENTS_DOCUMENTATION.md`** - Detailed documentation and examples
- **`style.css`** - Styling for the component

## Data Structure

```javascript
// Each day follows this structure:
{
  day: "Monday",           // Day name
  is_closed: false,        // Whether closed
  is_open_24: false,       // Whether 24/7 (future feature)
  hours: [                 // Time slots array
    {
      open_time: "09:00",  // Opening time
      close_time: "17:00"  // Closing time
    }
  ]
}
```

## Event Triggers

Events are automatically dispatched when:
- ✅ Day toggles are changed (on/off)
- ✅ Time values are modified
- ✅ Time slots are added/removed
- ✅ "Same as Business Hours" is toggled

## Integration Examples

### Save to Server
```javascript
document.addEventListener('businessHoursChange', function(event) {
  fetch('/api/save-hours', {
    method: 'POST',
    body: JSON.stringify(event.detail.allData)
  });
});
```

### Form Validation
```javascript
document.addEventListener('businessHoursChange', function(event) {
  const isValid = event.detail.allData.validation.businessValid;
  document.getElementById('submit-btn').disabled = !isValid;
});
```

### Local Storage
```javascript
document.addEventListener('businessHoursChange', function(event) {
  localStorage.setItem('hours', JSON.stringify(event.detail.allData));
});
```

## Browser Support

Works in all modern browsers that support:
- CustomEvent API
- ES6 features
- DOM manipulation

## Demo

Open `example-usage.html` in your browser to see the custom events in action with:
- Real-time event logging
- Data display updates
- Interactive controls
- Complete integration example

## Migration from Previous Version

The component is backward compatible. Existing code will continue to work, and you can gradually add event listeners to take advantage of the new features.

## Similar Implementation

This event system follows the same pattern as `custom-select.js`, providing consistency across components in your application.
