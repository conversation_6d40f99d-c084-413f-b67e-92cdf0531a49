<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Business Hours Manager - Custom Events Example</title>
    <link rel="stylesheet" href="style.css" />
    <style>
      .example-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .data-display {
        background: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }

      .data-display h3 {
        margin-top: 0;
        color: #333;
      }

      .data-display pre {
        background: white;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
        line-height: 1.4;
      }

      .event-log {
        background: #e8f4fd;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        max-height: 300px;
        overflow-y: auto;
      }

      .event-log h3 {
        margin-top: 0;
        color: #0066cc;
      }

      .event-item {
        background: white;
        padding: 10px;
        margin: 5px 0;
        border-radius: 4px;
        border-left: 4px solid #0066cc;
        font-size: 12px;
      }

      .event-time {
        color: #666;
        font-weight: bold;
      }

      .event-type {
        color: #0066cc;
        font-weight: bold;
      }

      .controls {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }

      .controls button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }

      .controls button:hover {
        background: #0056b3;
      }
    </style>
  </head>
  <body>
    <div class="example-container">
      <h1>Business Hours Manager - Custom Events Example</h1>
      <p>
        This example demonstrates how to use the BusinessHoursManager custom
        events to access and render data on the page.
      </p>

      <!-- Controls for testing -->
      <div class="controls">
        <h3>Test Controls</h3>
        <button onclick="getBusinessHours()">Get Business Hours</button>
        <button onclick="getScannerHours()">Get Scanner Hours</button>
        <button onclick="getAllData()">Get All Data</button>
        <button onclick="clearEventLog()">Clear Event Log</button>
      </div>

      <!-- Event Log -->
      <div class="event-log">
        <h3>Event Log</h3>
        <div id="eventLog">
          <p>
            Events will appear here when you interact with the business hours
            form...
          </p>
        </div>
      </div>

      <!-- Data Display -->
      <div class="data-display">
        <h3>Current Data</h3>
        <pre id="dataDisplay">
No data loaded yet. Interact with the form below to see data updates.</pre
        >
      </div>

      <!-- Business Hours Form -->
      <div class="form-container">
        <h2>Business hours of operation</h2>
        <div id="business-hours"></div>

        <h2>Scanner hours of operation</h2>
        <div class="same-hours-container">
          <label>
            <input type="checkbox" id="same-hours" name="same-hours" />
            Same as Business Hours of Operation
          </label>
        </div>
        <div id="scanner-hours"></div>
      </div>
    </div>

    <script src="business-hours-manager.js"></script>
    <script>
      // Event listeners for custom events
      document.addEventListener("businessHoursChange", function (event) {
        logEvent("businessHoursChange", event.detail);
        updateDataDisplay(event.detail);
      });

      document.addEventListener("businessHoursOnlyChange", function (event) {
        logEvent("businessHoursOnlyChange", event.detail);
      });

      document.addEventListener("scannerHoursOnlyChange", function (event) {
        logEvent("scannerHoursOnlyChange", event.detail);
      });

      // Helper functions
      function logEvent(eventType, data) {
        const eventLog = document.getElementById("eventLog");
        const eventItem = document.createElement("div");
        eventItem.className = "event-item";

        const timestamp = new Date().toLocaleTimeString();
        eventItem.innerHTML = `
                <div class="event-time">${timestamp}</div>
                <div class="event-type">${eventType}</div>
                <div>Data keys: ${Object.keys(data).join(", ")}</div>
            `;

        eventLog.insertBefore(eventItem, eventLog.firstChild);

        // Keep only last 10 events
        while (eventLog.children.length > 10) {
          eventLog.removeChild(eventLog.lastChild);
        }
      }

      function updateDataDisplay(data) {
        const dataDisplay = document.getElementById("dataDisplay");
        dataDisplay.textContent = JSON.stringify(data, null, 2);
      }

      function getBusinessHours() {
        const data = BusinessHoursManager.getBusinessHours();
        console.log("Business Hours:", data);
        updateDataDisplay({ businessHours: data });
      }

      function getScannerHours() {
        const data = BusinessHoursManager.getScannerHours();
        console.log("Scanner Hours:", data);
        updateDataDisplay({ scannerHours: data });
      }

      function getAllData() {
        const data = BusinessHoursManager.getAllData();
        console.log("All Data:", data);
        updateDataDisplay(data);
      }

      function clearEventLog() {
        const eventLog = document.getElementById("eventLog");
        eventLog.innerHTML =
          "<p>Event log cleared. Interact with the form to see new events...</p>";
      }

      // Initialize when page loads
      document.addEventListener("DOMContentLoaded", function () {
        console.log("BusinessHoursManager initialized and ready for events!");
      });
    </script>
  </body>
</html>
