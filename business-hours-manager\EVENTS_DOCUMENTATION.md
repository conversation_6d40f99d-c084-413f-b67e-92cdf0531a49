# Business Hours Manager - Custom Events Documentation

The BusinessHoursManager now exposes custom events similar to the custom-select.js component, allowing external code to listen for changes and access business hours data in real-time.

## Available Custom Events

### 1. `businessHoursChange`
Dispatched whenever any change occurs in either business hours or scanner hours.

**Event Detail Structure:**
```javascript
{
  businessHours: Array,     // Business hours data
  scannerHours: Array,      // Scanner hours data  
  allData: Object,          // Complete data structure
  timestamp: String         // ISO timestamp of the event
}
```

### 2. `businessHoursOnlyChange`
Dispatched specifically when business hours are modified.

**Event Detail Structure:**
```javascript
{
  businessHours: Array,     // Business hours data
  type: 'business',         // Event type identifier
  timestamp: String         // ISO timestamp of the event
}
```

### 3. `scannerHoursOnlyChange`
Dispatched specifically when scanner hours are modified.

**Event Detail Structure:**
```javascript
{
  scannerHours: Array,      // Scanner hours data
  type: 'scanner',          // Event type identifier
  timestamp: String         // ISO timestamp of the event
}
```

## Data Structure

Each day's data follows this structure:
```javascript
{
  day: "Monday",           // Day name
  is_closed: false,        // Whether the day is closed
  is_open_24: false,       // Whether open 24 hours (future feature)
  hours: [                 // Array of time slots for the day
    {
      open_time: "09:00",  // Opening time
      close_time: "17:00"  // Closing time
    }
    // Multiple time slots possible
  ]
}
```

## Usage Examples

### Basic Event Listening
```javascript
// Listen for any business hours changes
document.addEventListener('businessHoursChange', function(event) {
  console.log('Business hours changed:', event.detail);
  
  const { businessHours, scannerHours, allData } = event.detail;
  
  // Update your UI with the new data
  updateBusinessHoursDisplay(businessHours);
  updateScannerHoursDisplay(scannerHours);
});

// Listen for specific business hours changes only
document.addEventListener('businessHoursOnlyChange', function(event) {
  console.log('Business hours only changed:', event.detail.businessHours);
});

// Listen for specific scanner hours changes only
document.addEventListener('scannerHoursOnlyChange', function(event) {
  console.log('Scanner hours only changed:', event.detail.scannerHours);
});
```

### Accessing Data Programmatically
```javascript
// Static methods to get data anytime
const businessHours = BusinessHoursManager.getBusinessHours();
const scannerHours = BusinessHoursManager.getScannerHours();
const allData = BusinessHoursManager.getAllData();

// Instance methods (if you have a reference to the instance)
const instance = BusinessHoursManager.getInstance();
if (instance) {
  const businessHours = instance.getBusinessHours();
  const scannerHours = instance.getScannerHours();
  const allData = instance.getData();
}
```

### Real-time Data Rendering
```javascript
function renderBusinessHours(hoursData) {
  const container = document.getElementById('hours-display');
  
  hoursData.forEach(dayData => {
    const dayElement = document.createElement('div');
    dayElement.className = 'day-hours';
    
    if (dayData.is_closed) {
      dayElement.innerHTML = `<strong>${dayData.day}:</strong> Closed`;
    } else {
      const timeSlots = dayData.hours.map(slot => 
        `${slot.open_time} - ${slot.close_time}`
      ).join(', ');
      dayElement.innerHTML = `<strong>${dayData.day}:</strong> ${timeSlots}`;
    }
    
    container.appendChild(dayElement);
  });
}

// Listen for changes and update display
document.addEventListener('businessHoursChange', function(event) {
  const container = document.getElementById('hours-display');
  container.innerHTML = ''; // Clear existing content
  renderBusinessHours(event.detail.businessHours);
});
```

### Form Validation Integration
```javascript
document.addEventListener('businessHoursChange', function(event) {
  const { allData } = event.detail;
  
  // Check validation status
  if (!allData.validation.businessValid) {
    showError('Please set at least one business day with hours');
  }
  
  if (!allData.validation.scannerValid) {
    showError('Please set at least one scanner day with hours');
  }
  
  // Enable/disable form submission based on validation
  const submitButton = document.getElementById('submit-btn');
  const isValid = allData.validation.businessValid && allData.validation.scannerValid;
  submitButton.disabled = !isValid;
});
```

## Event Triggers

Events are automatically dispatched when:
- A day is toggled on/off
- Time values are changed in dropdowns
- Additional time slots are added
- Time slots are removed
- The "Same as Business Hours" checkbox is toggled

## Integration with Other Components

The BusinessHoursManager events can be easily integrated with other form components:

```javascript
// Save to localStorage on changes
document.addEventListener('businessHoursChange', function(event) {
  localStorage.setItem('businessHoursData', JSON.stringify(event.detail.allData));
});

// Send to server on changes (with debouncing)
let saveTimeout;
document.addEventListener('businessHoursChange', function(event) {
  clearTimeout(saveTimeout);
  saveTimeout = setTimeout(() => {
    fetch('/api/save-hours', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(event.detail.allData)
    });
  }, 1000); // Debounce for 1 second
});
```

## Browser Compatibility

The custom events use the standard `CustomEvent` API, which is supported in all modern browsers. For older browsers, you may need a polyfill.

## Smart Time Selector Events (v2.0)

The enhanced Business Hours Manager includes smart time filtering with additional events for better integration.

### 4. `timeSlotChange`

Dispatched when individual time slots change, providing granular control and monitoring.

**Event Detail Structure:**
```javascript
{
  prefix: 'biz' | 'scan',           // Type of hours (business or scanner)
  day: 'monday' | 'tuesday' | ..., // Day of the week
  isEnabled: boolean,               // Whether the day is enabled
  slots: [                          // Array of time slots for the day
    {
      startTime: '09:00',           // Start time in 24-hour format
      endTime: '17:00',             // End time in 24-hour format
      index: 0                      // Slot index (0 for main, 1+ for additional)
    }
  ],
  canAddMore: boolean,              // Whether more slots can be added
  timestamp: '2025-01-01T12:00:00.000Z' // ISO timestamp
}
```

**Usage Example:**
```javascript
document.addEventListener('timeSlotChange', function(event) {
  const { prefix, day, slots, canAddMore } = event.detail;

  console.log(`${prefix} hours for ${day}:`, slots);

  if (!canAddMore) {
    console.log(`No more time slots available for ${day}`);
  }

  // Update UI based on slot changes
  updateTimeSlotDisplay(prefix, day, slots);
});
```

### 5. `smartBusinessHoursChange`

Comprehensive event that includes all smart filtering metadata and validation information.

**Event Detail Structure:**
```javascript
{
  allData: {                        // Complete hours data (same as businessHoursChange)
    business: [...],
    scanner: [...],
    sameHours: boolean,
    validation: { ... }
  },
  smartFiltering: {                 // Smart filtering metadata
    businessHours: {
      monday: {
        isEnabled: boolean,
        slotsCount: number,
        canAddMore: boolean,
        availableStartTimesCount: number,
        slots: [
          {
            startTime: '09:00',
            endTime: '17:00',
            index: 0,
            validation: {
              isValid: boolean,
              message: string
            }
          }
        ]
      },
      // ... other days
    },
    scannerHours: { /* same structure */ }
  },
  timestamp: '2025-01-01T12:00:00.000Z',
  version: '2.0'                    // Event version for compatibility
}
```

**Usage Example:**
```javascript
document.addEventListener('smartBusinessHoursChange', function(event) {
  const { allData, smartFiltering } = event.detail;

  // Access traditional data
  console.log('Business hours:', allData.business);

  // Access smart filtering data
  Object.keys(smartFiltering.businessHours).forEach(day => {
    const dayData = smartFiltering.businessHours[day];

    if (dayData.isEnabled) {
      console.log(`${day}: ${dayData.slotsCount} slots, can add more: ${dayData.canAddMore}`);

      // Check for validation issues
      dayData.slots.forEach(slot => {
        if (!slot.validation.isValid) {
          console.warn(`Validation issue in ${day} slot ${slot.index}: ${slot.validation.message}`);
        }
      });
    }
  });
});
```

## Smart Filtering Features

The enhanced Business Hours Manager includes the following smart filtering capabilities:

### Cross-Slot Time Filtering
- New time slots automatically start after the previous slot ends
- No overlapping time ranges allowed
- Dynamic updates when existing slots change

### Add Button State Management
- Add buttons are disabled when no more time slots are available
- Visual feedback with tooltips explaining why buttons are disabled
- Automatic re-enabling when slots become available

### UX Enhancements
- Inline warning messages for invalid time selections
- Auto-adjustment of invalid times (e.g., end time before start time)
- Smooth animations for warnings and state changes
- Enhanced visual feedback for user interactions

### Validation and Error Handling
- Real-time validation of time slot configurations
- Prevention of overlapping time ranges
- Clear error messages with suggested fixes
- Auto-correction where possible

## Migration from v1.0 to v2.0

The v2.0 events are backward compatible with v1.0. Existing code will continue to work, but you can enhance your integration by listening to the new events:

```javascript
// v1.0 - Still works
document.addEventListener('businessHoursChange', function(event) {
  // Handle basic changes
});

// v2.0 - Enhanced functionality
document.addEventListener('smartBusinessHoursChange', function(event) {
  if (event.detail.version === '2.0') {
    // Handle smart filtering features
    const { smartFiltering } = event.detail;
    // ... enhanced logic
  }
});

// v2.0 - Granular slot monitoring
document.addEventListener('timeSlotChange', function(event) {
  // Handle individual slot changes
});
```

## See Also

- `example-usage.html` - Complete working example
- `business-hours-manager.js` - Main implementation
- `custom-select.js` - Similar event system implementation
- `style.css` - Enhanced styling for smart features
