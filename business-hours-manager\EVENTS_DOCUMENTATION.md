# Business Hours Manager - Custom Events Documentation

The BusinessHoursManager now exposes custom events similar to the custom-select.js component, allowing external code to listen for changes and access business hours data in real-time.

## Available Custom Events

### 1. `businessHoursChange`
Dispatched whenever any change occurs in either business hours or scanner hours.

**Event Detail Structure:**
```javascript
{
  businessHours: Array,     // Business hours data
  scannerHours: Array,      // Scanner hours data  
  allData: Object,          // Complete data structure
  timestamp: String         // ISO timestamp of the event
}
```

### 2. `businessHoursOnlyChange`
Dispatched specifically when business hours are modified.

**Event Detail Structure:**
```javascript
{
  businessHours: Array,     // Business hours data
  type: 'business',         // Event type identifier
  timestamp: String         // ISO timestamp of the event
}
```

### 3. `scannerHoursOnlyChange`
Dispatched specifically when scanner hours are modified.

**Event Detail Structure:**
```javascript
{
  scannerHours: Array,      // Scanner hours data
  type: 'scanner',          // Event type identifier
  timestamp: String         // ISO timestamp of the event
}
```

## Data Structure

Each day's data follows this structure:
```javascript
{
  day: "Monday",           // Day name
  is_closed: false,        // Whether the day is closed
  is_open_24: false,       // Whether open 24 hours (future feature)
  hours: [                 // Array of time slots for the day
    {
      open_time: "09:00",  // Opening time
      close_time: "17:00"  // Closing time
    }
    // Multiple time slots possible
  ]
}
```

## Usage Examples

### Basic Event Listening
```javascript
// Listen for any business hours changes
document.addEventListener('businessHoursChange', function(event) {
  console.log('Business hours changed:', event.detail);
  
  const { businessHours, scannerHours, allData } = event.detail;
  
  // Update your UI with the new data
  updateBusinessHoursDisplay(businessHours);
  updateScannerHoursDisplay(scannerHours);
});

// Listen for specific business hours changes only
document.addEventListener('businessHoursOnlyChange', function(event) {
  console.log('Business hours only changed:', event.detail.businessHours);
});

// Listen for specific scanner hours changes only
document.addEventListener('scannerHoursOnlyChange', function(event) {
  console.log('Scanner hours only changed:', event.detail.scannerHours);
});
```

### Accessing Data Programmatically
```javascript
// Static methods to get data anytime
const businessHours = BusinessHoursManager.getBusinessHours();
const scannerHours = BusinessHoursManager.getScannerHours();
const allData = BusinessHoursManager.getAllData();

// Instance methods (if you have a reference to the instance)
const instance = BusinessHoursManager.getInstance();
if (instance) {
  const businessHours = instance.getBusinessHours();
  const scannerHours = instance.getScannerHours();
  const allData = instance.getData();
}
```

### Real-time Data Rendering
```javascript
function renderBusinessHours(hoursData) {
  const container = document.getElementById('hours-display');
  
  hoursData.forEach(dayData => {
    const dayElement = document.createElement('div');
    dayElement.className = 'day-hours';
    
    if (dayData.is_closed) {
      dayElement.innerHTML = `<strong>${dayData.day}:</strong> Closed`;
    } else {
      const timeSlots = dayData.hours.map(slot => 
        `${slot.open_time} - ${slot.close_time}`
      ).join(', ');
      dayElement.innerHTML = `<strong>${dayData.day}:</strong> ${timeSlots}`;
    }
    
    container.appendChild(dayElement);
  });
}

// Listen for changes and update display
document.addEventListener('businessHoursChange', function(event) {
  const container = document.getElementById('hours-display');
  container.innerHTML = ''; // Clear existing content
  renderBusinessHours(event.detail.businessHours);
});
```

### Form Validation Integration
```javascript
document.addEventListener('businessHoursChange', function(event) {
  const { allData } = event.detail;
  
  // Check validation status
  if (!allData.validation.businessValid) {
    showError('Please set at least one business day with hours');
  }
  
  if (!allData.validation.scannerValid) {
    showError('Please set at least one scanner day with hours');
  }
  
  // Enable/disable form submission based on validation
  const submitButton = document.getElementById('submit-btn');
  const isValid = allData.validation.businessValid && allData.validation.scannerValid;
  submitButton.disabled = !isValid;
});
```

## Event Triggers

Events are automatically dispatched when:
- A day is toggled on/off
- Time values are changed in dropdowns
- Additional time slots are added
- Time slots are removed
- The "Same as Business Hours" checkbox is toggled

## Integration with Other Components

The BusinessHoursManager events can be easily integrated with other form components:

```javascript
// Save to localStorage on changes
document.addEventListener('businessHoursChange', function(event) {
  localStorage.setItem('businessHoursData', JSON.stringify(event.detail.allData));
});

// Send to server on changes (with debouncing)
let saveTimeout;
document.addEventListener('businessHoursChange', function(event) {
  clearTimeout(saveTimeout);
  saveTimeout = setTimeout(() => {
    fetch('/api/save-hours', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(event.detail.allData)
    });
  }, 1000); // Debounce for 1 second
});
```

## Browser Compatibility

The custom events use the standard `CustomEvent` API, which is supported in all modern browsers. For older browsers, you may need a polyfill.

## See Also

- `example-usage.html` - Complete working example
- `business-hours-manager.js` - Main implementation
- `custom-select.js` - Similar event system implementation
