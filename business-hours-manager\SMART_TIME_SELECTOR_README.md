# Smart Time Selector System

A comprehensive enhancement to the Business Hours Manager that provides intelligent time slot filtering, dynamic validation, and enhanced user experience for business hours configuration.

## 🚀 Features

### Smart Cross-Slot Filtering
- **Dynamic Start Time Filtering**: New time slots automatically show only valid start times (after previous slot ends)
- **Real-time Updates**: When existing slots change, all subsequent slots update their available options
- **Overlap Prevention**: System prevents overlapping time ranges with intelligent validation

### Add Button State Management
- **Intelligent Disabling**: Add buttons disable when no more time slots are available
- **Visual Feedback**: Clear tooltips explain why buttons are disabled
- **Auto Re-enabling**: Buttons automatically re-enable when slots become available

### UX Enhancements
- **Inline Warnings**: Real-time validation messages with smooth animations
- **Auto-Adjustment**: Invalid times are automatically corrected when possible
- **Enhanced Visual Feedback**: Hover effects, focus states, and loading indicators
- **Responsive Design**: Mobile-friendly interface with adaptive layouts

### Custom Events System
- **timeSlotChange**: Granular events for individual slot changes
- **smartBusinessHoursChange**: Comprehensive events with filtering metadata
- **Backward Compatibility**: Maintains compatibility with existing event listeners

## 📋 Behavior Rules

### End Time Filtering (Per Row)
When a user selects a Start time (e.g., 06:00 AM), the End time dropdown only shows times after the selected start (e.g., 06:30 AM, 07:00 AM, etc.).

### New Slot Filtering (Next Row)
When adding a new time slot:
- The new slot's Start time dropdown only shows times after the previous slot's End time
- Example: Slot 1 → 06:00 AM - 11:00 AM, Slot 2 → available start times: 11:30 AM, 12:00 PM, etc.

### Dynamic Updates
- If a user changes a previously selected time, all subsequent slot dropdowns automatically update
- Real-time validation prevents invalid configurations

### Validation
- Prevents overlapping or duplicate time ranges
- Disables add button when no remaining time slots are available
- Shows inline warning messages for invalid configurations

## 🛠️ Installation

1. Include the enhanced business hours manager files:
```html
<link rel="stylesheet" href="style.css">
<script src="business-hours-manager.js"></script>
```

2. Initialize the manager:
```javascript
const businessHoursManager = new BusinessHoursManager();
businessHoursManager.init();
```

## 📖 Usage Examples

### Basic Implementation
```html
<div class="bd-container">
    <div id="business-hours"></div>
    <div id="scanner-hours"></div>
</div>
```

### Event Listening
```javascript
// Listen for individual slot changes
document.addEventListener('timeSlotChange', function(event) {
    const { prefix, day, slots, canAddMore } = event.detail;
    console.log(`${prefix} hours for ${day}:`, slots);
});

// Listen for comprehensive changes with smart filtering data
document.addEventListener('smartBusinessHoursChange', function(event) {
    const { allData, smartFiltering } = event.detail;
    
    // Access traditional business hours data
    console.log('Business hours:', allData.business);
    
    // Access smart filtering metadata
    Object.keys(smartFiltering.businessHours).forEach(day => {
        const dayData = smartFiltering.businessHours[day];
        if (dayData.isEnabled) {
            console.log(`${day}: ${dayData.slotsCount} slots, can add more: ${dayData.canAddMore}`);
        }
    });
});
```

### Manual Validation
```javascript
// Validate a specific time slot
const validation = businessHoursManager.validateTimeSlot('biz', 'monday', 0);
if (!validation.isValid) {
    console.log('Validation error:', validation.message);
}

// Check if more slots can be added
const canAdd = businessHoursManager.canAddMoreSlots('biz', 'monday');
console.log('Can add more slots:', canAdd);
```

### Custom Warnings
```javascript
// Show a custom warning
businessHoursManager.showTimeSlotWarning('biz', 'monday', 0, 'Custom warning message');

// Clear warnings
businessHoursManager.clearTimeSlotWarning('biz', 'monday', 0);
```

## 🧪 Testing

The system includes comprehensive test cases covering all functionality:

1. **Open the test file**: `smart-time-selector-tests.html`
2. **Run tests**: Click "Run All Tests" to execute the complete test suite
3. **Monitor events**: View real-time event logging in the test interface

### Test Coverage
- Cross-slot time filtering
- Dynamic slot updates
- Overlap prevention
- Add button state management
- UX enhancements and validation
- Custom events system

## 🎨 Styling

The system includes enhanced CSS with:
- Smooth animations for warnings and state changes
- Responsive design for mobile devices
- Enhanced focus states for accessibility
- Loading indicators for dynamic updates

### Custom CSS Classes
- `.time-slot-warning`: Warning message styling
- `.time-slot-warning.success`: Success message variant
- `.add-hours-btn:disabled`: Disabled button styling
- `.time-select.updating`: Loading state for selects

## 🔧 API Reference

### Core Methods

#### `getDayTimeSlots(prefix, day)`
Returns all time slots for a specific day in chronological order.

#### `getAvailableStartTimesForNewSlot(prefix, day)`
Gets available start time options for a new slot based on existing slots.

#### `updateSubsequentSlots(prefix, day, changedSlotIndex)`
Updates all subsequent time slots when an earlier slot changes.

#### `canAddMoreSlots(prefix, day)`
Checks if more time slots can be added for the day.

#### `validateTimeSlot(prefix, day, slotIndex)`
Validates a time slot and returns validation result with message.

#### `showTimeSlotWarning(prefix, day, slotIndex, message)`
Displays an inline warning message for a time slot.

#### `clearTimeSlotWarning(prefix, day, slotIndex)`
Removes warning message for a time slot.

#### `autoAdjustTimeSlot(prefix, day, slotIndex)`
Attempts to auto-adjust invalid time selections.

### Event Details

#### timeSlotChange Event
```javascript
{
  prefix: 'biz' | 'scan',
  day: string,
  isEnabled: boolean,
  slots: Array<{startTime: string, endTime: string, index: number}>,
  canAddMore: boolean,
  timestamp: string
}
```

#### smartBusinessHoursChange Event
```javascript
{
  allData: Object,           // Complete hours data
  smartFiltering: Object,    // Smart filtering metadata
  timestamp: string,
  version: '2.0'
}
```

## 🔄 Migration Guide

### From v1.0 to v2.0

The smart time selector is fully backward compatible. Existing implementations will continue to work without changes.

To take advantage of new features:

1. **Update event listeners** to use new event types
2. **Enable smart filtering** by ensuring proper HTML structure
3. **Add CSS enhancements** for improved visual feedback

### Breaking Changes
None. All existing functionality is preserved.

## 🐛 Troubleshooting

### Common Issues

**Add button not working**
- Check if day is enabled (toggle checked)
- Verify available time slots exist
- Ensure proper HTML structure with data attributes

**Events not firing**
- Verify event listeners are attached after DOM ready
- Check browser console for JavaScript errors
- Ensure BusinessHoursManager is properly initialized

**Styling issues**
- Include the updated style.css file
- Check for CSS conflicts with existing styles
- Verify responsive breakpoints for mobile devices

## 📚 Documentation

- `EVENTS_DOCUMENTATION.md` - Detailed event system documentation
- `smart-time-selector-tests.html` - Interactive test suite
- `example-usage.html` - Basic usage examples

## 🤝 Contributing

When contributing to the smart time selector system:

1. Follow the existing code patterns and naming conventions
2. Add comprehensive tests for new functionality
3. Update documentation for any API changes
4. Ensure backward compatibility is maintained

## 📄 License

This enhancement maintains the same license as the original Business Hours Manager component.
