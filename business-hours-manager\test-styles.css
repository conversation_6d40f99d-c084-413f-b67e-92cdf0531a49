/* Smart Time Selector Test Styles */

.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
}

.test-section h2 {
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-case {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.test-case h3 {
  color: #495057;
  margin-top: 0;
}

.test-result {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  font-weight: bold;
}

.test-result.pass {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.test-result.fail {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.test-result.pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.event-log {
  background-color: #f1f3f4;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.event-log-entry {
  margin: 2px 0;
  padding: 2px 4px;
  border-radius: 2px;
}

.event-log-entry.timeSlotChange {
  background-color: #e3f2fd;
}

.event-log-entry.smartBusinessHoursChange {
  background-color: #f3e5f5;
}

.controls {
  margin: 20px 0;
  padding: 15px;
  background-color: #e9ecef;
  border-radius: 4px;
}

.controls button {
  margin: 5px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.controls button:hover {
  background-color: #0056b3;
}

.controls button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.controls button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
  .test-container {
    padding: 10px;
  }
  
  .test-section {
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .test-case {
    padding: 10px;
    margin: 15px 0;
  }
  
  .controls {
    padding: 10px;
  }
  
  .controls button {
    display: block;
    width: 100%;
    margin: 5px 0;
  }
  
  .event-log {
    font-size: 11px;
    max-height: 150px;
  }
}

/* Print styles */
@media print {
  .controls {
    display: none;
  }
  
  .test-section {
    break-inside: avoid;
    border: 1px solid #000;
  }
  
  .test-result.pass {
    background-color: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .test-result.fail {
    background-color: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .test-result.pending {
    background-color: #f0f0f0 !important;
    color: #000 !important;
  }
}

/* Enhanced accessibility */
.test-result:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.event-log:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Animation for test results */
.test-result {
  transition: all 0.3s ease;
}

.test-result.pass {
  animation: successPulse 0.5s ease-out;
}

.test-result.fail {
  animation: errorShake 0.5s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Loading state for tests */
.test-result.running {
  background-color: #cce5ff;
  color: #004085;
  border: 1px solid #99d3ff;
}

.test-result.running::after {
  content: " ⏳";
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
